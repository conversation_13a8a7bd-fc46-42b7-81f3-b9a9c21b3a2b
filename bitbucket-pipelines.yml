# Workflow Configuration

image: node:22

definitions:
  services:
    docker:
      memory: 2560
  caches:
    nodecustom:
      key:
        files:
          - pnpm-lock.yaml
      path: ./node_modules
  steps:
    - step: &build_and_test
        #        runs-on: self.hosted
        name: Build and Test
        deployment: staging
        caches:
          - nodecustom
        script:
          - echo $BITBUCKET_BRANCH
          - if [ -z ${BITBUCKET_BRANCH+x} ]; then exit 1; fi
          - npm install -g pnpm
          - pnpm install --frozen-lockfile
          - VITE_BASE_URL=$AWS_BUCKET_PATH_URL/$BITBUCKET_BRANCH/ pnpm build --mode development
        artifacts:
          - public/dist/**



    - step: &build_prod
        name: Production Build
        deployment: production
        caches:
          - nodecustom
        script:
          - npm install -g pnpm
          - pnpm install --frozen-lockfile
          - VITE_BASE_URL=$AWS_BUCKET_PATH_URL/$BITBUCKET_BUILD_NUMBER/ pnpm prod
        artifacts:
          - public/dist/**

    - step: &deploy_branch
        #        runs-on: self.hosted
        name: Deploy Branch
        caches:
          - docker
        clone:
          enabled: false
        script:
          - pipe: atlassian/aws-s3-deploy:1.6.1
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: 'fra1'
              S3_BUCKET: "tmg/${AWS_BUCKET_PATH}/${BITBUCKET_BRANCH}"
              LOCAL_PATH: 'public/dist'
              EXTRA_ARGS: '--endpoint=https://fra1.digitaloceanspaces.com/ --acl=public-read'
              CACHE_CONTROL: 'max-age=60'

    - step: &deploy_prod
        name: Deploy
        caches:
          - docker
        clone:
          enabled: false
        script:
          - pipe: atlassian/aws-s3-deploy:1.6.1
            variables:
              AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
              AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
              AWS_DEFAULT_REGION: 'fra1'
              S3_BUCKET: "tmg/${AWS_BUCKET_PATH}/${BITBUCKET_BUILD_NUMBER}"
              LOCAL_PATH: 'public/dist'
              EXTRA_ARGS: '--endpoint=https://fra1.digitaloceanspaces.com/ --acl=public-read'

    - step: &add_prod_tag
        name: Tag version
        script:
          - git tag PROD-${BITBUCKET_BUILD_NUMBER} ${BITBUCKET_COMMIT}
          - git push origin --tags


pipelines:
  custom:
    create_dev_build_from_branch_only:
      - step: *build_and_test
      - step: *deploy_branch

    create_prod_build:
      - step: *build_prod
      - step: *deploy_prod
      - step: *add_prod_tag
