import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecordOrFail } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'LeadPreview'>> => {
    const lead = await findModelRecordOrFail('Lead', pk)

    return {
        ...createModelIdentification('LeadPreview', pk),
        id: lead.id,
        from_iata_code: lead.from_iata_code,
        to_iata_code: lead.to_iata_code,
        departure_date: lead.departure_date,
        return_date: lead.return_date,
        created_at: lead.created_at,
        client_pk: lead.client_pk,
    }
})
