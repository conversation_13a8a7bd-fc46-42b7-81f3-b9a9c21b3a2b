import { defineResourceHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecordOrFail } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'ProductAdditionalInfo'>> => {
    const product = await findModelRecordOrFail('Product', pk)

    return {
        ...createModelIdentification('ProductAdditionalInfo', pk),
        id: product.id,

        tmp_issued_with_label: 'CC **1234',
    }
})
