import { defineResourceHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecordOrFail } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'SalePreview'>> => {
    const record = await findModelRecordOrFail('Sale', pk)

    return {
        ...createModelIdentification('SalePreview', pk),
        id: record.id,
        is_split: record.is_split,
    }
})
