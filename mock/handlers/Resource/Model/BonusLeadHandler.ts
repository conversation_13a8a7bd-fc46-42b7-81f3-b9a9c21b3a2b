import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecordOrFail } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'BonusLead'>> => {
    const lead = await findModelRecordOrFail('Lead', pk)

    return {
        ...createModelIdentification('BonusLead', pk),
        ...lead,
        pq_count: randomInt(0, 10),
        spq_count: randomInt(0, 10),
        price_quote_last_time: dateToUnixTimestamp(faker.date.past()),
    }
})
