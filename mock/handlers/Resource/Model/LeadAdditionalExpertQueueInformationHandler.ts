import { defineResource<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { ExpertQueueDestination } from '~/api/models/Lead/LeadAdditionalExpertQueueInformation'
import { faker } from '@faker-js/faker'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'LeadAdditionalExpertQueueInformation'>> => {
    return {
        ...createModelIdentification('LeadAdditionalExpertQueueInformation', pk),
        id: Number(pk),
        follow_up: randomBoolean(),
        reached: randomBoolean(),
        rush: randomBoolean(),
        lead_price: withChance(randomInt(0, 20000), 0.9),
        destination: randomEnumValue(ExpertQueueDestination),
        expert_request_at: dateToUnixTimestamp(faker.date.past()),
    }
})
