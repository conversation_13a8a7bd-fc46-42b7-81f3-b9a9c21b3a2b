import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecordOrFail } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'ClientPreview'>> => {
    const client = await findModelRecordOrFail('Client', pk)

    return {
        ...createModelIdentification('ClientPreview', pk),
        id: client.id,
        first_name: client.first_name,
        last_name: client.last_name,
    }
})
