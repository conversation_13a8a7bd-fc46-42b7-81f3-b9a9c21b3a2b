import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'LeadAdditionalCallCounterInfo'>> => {
    const record = await findModelRecord('LeadAdditionalCallCounterInfo', pk)

    if (record) {
        return record
    }

    return {
        ...createModelIdentification('LeadAdditionalCallCounterInfo', pk),
        count: randomInt(0, 200),
        id: Number(pk),

    }
})
