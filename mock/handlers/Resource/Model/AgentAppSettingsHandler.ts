import { defineResource<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'AgentAppSettings'>> => {
    const [agentPk, settingName] = pk.split(':')

    const settingRecord = await findModelRecord('AgentAppSettings', pk)

    if (settingRecord) {
        return settingRecord
    }

    return {
        ...createModelIdentification('AgentAppSettings', pk),
        name: settingName,
        agent_pk: agentPk,
        value: null,
    }
})
