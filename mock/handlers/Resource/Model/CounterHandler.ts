import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import type { CounterCategory } from '~/api/models/Counter/Counter'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { withChance } from '~mock/lib/Helper/SeedHelper'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'Counter'>> => {
    const [, category] = pk.split(':')

    return {
        ...createModelIdentification('Counter', pk),
        category: category as CounterCategory,
        value: withChance(randomInt(0, 20), 0.5) ?? 0,
    }
})
