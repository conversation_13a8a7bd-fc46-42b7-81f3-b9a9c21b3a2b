import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { ReleasePostStatus } from '~/api/models/ReleasePost/ReleasePost'
import { sortWithResolvers } from '~/lib/Helper/ArrayHelper'

export default defineResourceHandler(async (resourceName, pk): Promise<ModelAttributes<'ReleasesInfo'>> => {
    let releases = await findModelRecords('ReleasePost', { status: ReleasePostStatus.Published })

    releases = sortWithResolvers(releases, {
        published_at: 'desc',
    })

    return {
        ...createModelIdentification('ReleasesInfo', pk),
        last_published_release_number: releases[0]?.id || null,
        type: 'all',
    }
})
