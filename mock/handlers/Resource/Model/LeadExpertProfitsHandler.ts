import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomFloat } from '~/lib/Helper/NumberHelper'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'LeadExpertProfits'>> => {
    const record = await findModelRecord('LeadExpertProfits', pk)

    if (record) {
        return record
    }

    const [lead_pk, agent_pk] = pk.split(':')

    return {
        ...createModelIdentification('LeadExpertProfits', pk),
        lead_pk,
        agent_pk,
        tkt_profit: randomFloat(10, 1000),
        lead_gp: randomFloat(10, 1000),
        pq_gp: randomFloat(10, 1000),
        is_manually_taken_from_queue: randomBoolean(),
    }
})
