import { defineResource<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

export default defineResourceHandler(async (_, pk): Promise<ModelAttributes<'LeadAdditionalFollowUp'>> => {
    const number1 = randomBoolean()
    const number2 = randomBoolean()
    const number3 = randomBoolean()

    return {
        ...createModelIdentification('LeadAdditionalFollowUp', pk),
        id: Number(pk),
        number1,
        number2,
        number3,

        canSendNumber1: !number1,
        canSendNumber2: !number2 && number1,
        canSendNumber3: !number3 && number2 && number1,
    }
})
