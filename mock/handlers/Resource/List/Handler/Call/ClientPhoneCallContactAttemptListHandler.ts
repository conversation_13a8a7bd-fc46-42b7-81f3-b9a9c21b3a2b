import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'CallContactAttempt'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)

    return {
        ...createModelIdentification('ClientPhoneCallContactAttemptList', pk),
        list: records.splice(0, randomInt(0, 5)).map(usePk),
    }
})
