import { defineResourceHand<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'
import { IssueCategory } from '~/api/models/Issue/Issue'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceHandler(async (resource, pk) => {
    const [agent_pk] = pk.split(':')

    const issues = await findModelRecords('Issue', (record) => {
        return [IssueCategory.ClientStatus, IssueCategory.SplitSale].includes(record.category)
    })

    const issuePks = pluckPks(issues)

    const chats = await findModelRecords('Chat', (record) => {
        return record.model_name === 'Issue' && issuePks.includes(record.model_pk)
    })

    return {
        ...createModelIdentification('Chat', pk),
        agent_pk,
        list: pluckPks(issues),
        chat_list: pluckPks(chats),
    } satisfies ModelAttributes<'ActiveIssueListWithChats'>
})
