import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'
import type { ModelAttributes } from '~types/lib/Model'

export default defineResourceListHandler(async (resource, pk) => {
    const [agent_pk] = pk.split(':')

    const sales = await findModelRecords('Sale')

    const salePks = pluckPks(sales)

    const chats = await findModelRecords('Chat', (record) => {
        return record.model_name === 'Sale' && salePks.includes(record.model_pk)
    })

    return {
        ...createModelIdentification('Chat', pk),
        agent_pk,
        list: salePks,
        chat_list: pluckPks(chats),
    } satisfies ModelAttributes<'ActiveCSIssueListWithChats'>
})
