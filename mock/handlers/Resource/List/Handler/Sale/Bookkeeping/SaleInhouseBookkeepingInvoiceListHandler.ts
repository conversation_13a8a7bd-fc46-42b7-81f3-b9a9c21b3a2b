import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'BookkeepingInvoice'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)
    const filter = records.filter(invoice => invoice.is_inhouse)

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(filter),
    }
})
