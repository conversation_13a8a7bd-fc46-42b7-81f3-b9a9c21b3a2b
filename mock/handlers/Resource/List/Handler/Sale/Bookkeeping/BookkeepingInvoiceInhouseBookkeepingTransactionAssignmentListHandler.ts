import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'BookkeepingTransactionAssignment'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName) // todo:

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(records),
    }
})
