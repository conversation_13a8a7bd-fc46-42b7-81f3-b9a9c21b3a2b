import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'Voucher'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(records),
    }
})
