import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'ProjectCard'

export default defineResourceListHandler(async (resource, pk) => {
    const records = (await findModelRecords('ProjectCard')).filter(record => !!record.sale_pk)

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(records),
    }
})
