import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'SaleClientRequest'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)
    const saleClientRequestPks = records.map((saleClientRequest) => (usePk(saleClientRequest)))

    return {
        ...createModelIdentification(modelName, pk),
        list: randomElements(saleClientRequestPks, randomInt(2, 4)),
    }
})
