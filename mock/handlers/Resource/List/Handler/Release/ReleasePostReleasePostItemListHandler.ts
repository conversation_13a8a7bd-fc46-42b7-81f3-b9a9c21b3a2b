import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'ReleasePostReleasePostItemList'

export default defineResourceListHandler(async (resource, pk) => {
    const records = (await findModelRecords('ReleasePostItem', {
        release_post_pk: pk,
    }))

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(records.slice().sort((a, b) => a.position - b.position)),
    }
})
