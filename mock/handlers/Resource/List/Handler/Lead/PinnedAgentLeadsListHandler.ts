import { defineResourceListHandler } from '~mock/utils/define'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'Lead'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)
    const leadPks = records.map((lead) => (usePk(lead)))

    return {
        ...createModelIdentification(modelName, pk),
        list: randomElements(leadPks, randomInt(2, 4)),
    }
})
