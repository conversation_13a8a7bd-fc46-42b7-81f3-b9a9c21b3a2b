import { defineResourceList<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'Lead'

export default defineResourceListHandler(async (resource, pk) => {
    const issues = await findModelRecords('Issue')
    const issuePks = issues.map(usePk)

    return {
        ...createModelIdentification(modelName, pk),
        list: randomElements(issuePks, randomInt(1, 5)),
    }
})
