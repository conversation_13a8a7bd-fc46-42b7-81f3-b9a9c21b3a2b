import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords('MilePriceProgram')

    return {
        ...createModelIdentification('ExternalUserAgentMilePriceProgramList', pk),
        list: records.splice(1, randomInt(0, 5)).map(usePk),
    }
})
