import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'ChatMessage'

export default defineResourceListHandler(async (resource, pk) => {
    const [chat_pk, auth_pk] = pk.split(':')

    const records = await findModelRecords(modelName, {
        chat_pk,
    })

    // @todo Filter by auth_pk

    return {
        ...createModelIdentification(modelName, pk),
        list: records.map((item) => item._pk),
    }
})
