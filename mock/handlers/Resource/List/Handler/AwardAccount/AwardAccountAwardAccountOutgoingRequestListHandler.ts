import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks } from '~/lib/Helper/ArrayHelper'

const modelName = 'AwardAccountOutgoingRequest'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName, {
        award_account_pk: pk,
    })

    return {
        ...createModelIdentification(modelName, pk),
        list: pluckPks(records),
    }
})
