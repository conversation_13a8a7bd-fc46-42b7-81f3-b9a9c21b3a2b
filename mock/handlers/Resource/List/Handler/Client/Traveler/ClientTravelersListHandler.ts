import { defineResourceListHand<PERSON> } from '~mock/utils/define'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'Traveler'

export default defineResourceListHandler(async (resource, pk) => {
    const records = await findModelRecords(modelName)
    const travelerPks = records.map((traveler) => (usePk(traveler)))

    return {
        ...createModelIdentification(modelName, pk),
        list: randomElements(travelerPks, randomInt(2, 4)),
    }
})
