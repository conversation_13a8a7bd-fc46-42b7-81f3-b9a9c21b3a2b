import { defineResource<PERSON>ist<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { useModelDefinition } from '~/composables/_core/useModelDefinition'
import { Model } from '~types/lib/Model'
import { isHasManyThroughRelation, isMorphPrimaryKeyDefinition } from '~/lib/Model/ModelController'

export default function RelationListHandler<
    TModelName extends ModelName,
    TRelationField extends Model.Relation.Field<TModelName>
>(modelName: TModelName, relationField: TRelationField) {
    const definition = useModelDefinition(modelName as string)

    const relation = definition.relations[relationField as string]

    if (!relation) {
        throw new Error(`Relation "${String(relationField)}" not found in model ${modelName}`)
    }

    if (!isHasManyThroughRelation(relation)) {
        throw new Error(`Relation "${String(relationField)}" is not a HasManyThrough relation in model "${modelName}"`)
    }

    return defineResourceListHandler(async (resource, pk) => {
        let conditions: AnyObject

        if (isMorphPrimaryKeyDefinition(relation.foreignKey)) {
            conditions = {
                model_name: modelName,
                model_pk: pk,
            }
        } else if (!Array.isArray(relation.foreignKey)) {
            conditions = {
                [relation.foreignKey]: pk,
            }
        } else {
            // @todo Composite foreign key
            throw new Error(`Composite foreign key not supported in HasManyThrough relation "${String(relationField)}" in model "${modelName}"`)
        }

        const records = await findModelRecords(relation.model, conditions)

        return {
            ...createModelIdentification(resource, pk),
            list: records.map((item) => item._pk),
        }
    })
}
