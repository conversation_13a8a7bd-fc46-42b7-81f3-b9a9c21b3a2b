import { defineResourceListHandler } from '~mock/utils/define'
import { createModelIdentification, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'

export default function ModelListHandler(modelName: ModelName) {
    return defineResourceListHandler(async (resource, pk) => {
        const records = await findModelRecords(modelName)

        const pkParts = pk.split(':')

        if (pkParts.length !== 2) {
            throw new Error(`ModelListHandler: pk must contain all primary key parts separated by ':'`)
        }

        return {
            ...createModelIdentification(modelName, pk),
            list: records.map((item) => item._pk),
        }
    })
}
