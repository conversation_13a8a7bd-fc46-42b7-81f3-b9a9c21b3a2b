import { defineResource<PERSON>ist<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelIdentification, findPivotRecords, getPkCondition } from '~mock/lib/Helper/ModelDatabaseHelper'
import { useModelDefinition } from '~/composables/_core/useModelDefinition'
import { Model } from '~types/lib/Model'
import { composePk } from '~/composables/usePk'
import { isHasManyThroughPivotRelation } from '~/lib/Model/ModelController'

export default function PivotListHandler<
    TModelName extends ModelName,
    TRelationField extends Model.Relation.Field<TModelName>
>(modelName: TModelName, relationField: TRelationField) {
    const definition = useModelDefinition(modelName as string)

    const relation = definition.relations[relationField as string]

    if (!relation) {
        throw new Error(`Relation "${String(relationField)}" not found in model ${modelName}`)
    }

    if (!isHasManyThroughPivotRelation(relation)) {
        throw new Error(`Relation "${String(relationField)}" is not a HasManyThroughPivot relation in model "${modelName}"`)
    }

    return defineResourceListHandler(async (resource, pk) => {
        const conditions = getPkCondition(relation.model, relation.parentKey, pk)

        const records = await findPivotRecords(relation.via, conditions)

        return {
            ...createModelIdentification(modelName, pk),
            list: records.map((item) => composePk(item, relation.childKey)).filter(Boolean) as PrimaryKey[],
        }
    })
}
