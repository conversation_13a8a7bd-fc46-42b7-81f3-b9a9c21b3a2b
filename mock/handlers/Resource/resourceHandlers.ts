// This file is auto generatedby vite-plugin-import-resource-handlers
// Do not edit this file manually

import type { MockResourceHandler } from '~mock/types'

import ActiveCSIssueListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Issue/ActiveCSIssueListWithChatsHandler'
import ActiveIssueListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Issue/ActiveIssueListWithChatsHandler'
import ActiveSaleListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Sale/ActiveSaleListWithChatsHandler'
import AgentAgentSkillListHandler from '#/mock/handlers/Resource/List/Handler/Agent/AgentAgentSkillListHandler'
import AgentAppSettingsHandler from '#/mock/handlers/Resource/Model/AgentAppSettingsHandler'
import AwardAccountAwardAccountIncomingRequestListHandler from '#/mock/handlers/Resource/List/Handler/AwardAccount/AwardAccountAwardAccountIncomingRequestListHandler'
import AwardAccountAwardAccountOutgoingRequestListHandler from '#/mock/handlers/Resource/List/Handler/AwardAccount/AwardAccountAwardAccountOutgoingRequestListHandler'
import BonusLeadHandler from '#/mock/handlers/Resource/Model/BonusLeadHandler'
import BookkeepingInvoiceInhouseBookkeepingTransactionAssignmentListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/BookkeepingInvoiceInhouseBookkeepingTransactionAssignmentListHandler'
import BookkeepingTransactionInhouseBookkeepingTransactionAssignmentListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/BookkeepingTransactionInhouseBookkeepingTransactionAssignmentListHandler'
import ChatChatMessageListHandler from '#/mock/handlers/Resource/List/Handler/Chat/ChatChatMessageListHandler'
import ChatChatMessagePinnedListHandler from '#/mock/handlers/Resource/List/Handler/Chat/ChatChatMessagePinnedListHandler'
import ChatRecentListHandler from '#/mock/handlers/Resource/List/Handler/Chat/ChatRecentListHandler'
import ClientClientEmailDataListHandler from '#/mock/handlers/Resource/List/Handler/Client/ClientClientEmailDataListHandler'
import ClientClientPhoneDataListHandler from '#/mock/handlers/Resource/List/Handler/Client/ClientClientPhoneDataListHandler'
import ClientPhoneCallContactAttemptListHandler from '#/mock/handlers/Resource/List/Handler/Call/ClientPhoneCallContactAttemptListHandler'
import ClientPreviewHandler from '#/mock/handlers/Resource/Model/ClientPreviewHandler'
import ClientTravelersListHandler from '#/mock/handlers/Resource/List/Handler/Client/Traveler/ClientTravelersListHandler'
import CounterHandler from '#/mock/handlers/Resource/Model/CounterHandler'
import DepartmentSkillListHandler from '#/mock/handlers/Resource/List/Handler/Skill/DepartmentSkillListHandler'
import EmailEmailContactAttemptConversationListHandler from '#/mock/handlers/Resource/List/Handler/ContactAttempt/EmailEmailContactAttemptConversationListHandler'
import ExternalUserAgentMilePriceProgramListHandler from '#/mock/handlers/Resource/List/Handler/ExternalUserAgent/ExternalUserAgentMilePriceProgramListHandler'
import LeadAdditionalExpertQueueInformationHandler from '#/mock/handlers/Resource/Model/LeadAdditionalExpertQueueInformationHandler'
import LeadAdditionalFollowUpHandler from '#/mock/handlers/Resource/Model/LeadAdditionalFollowUpHandler'
import LeadExpertProfitsHandler from '#/mock/handlers/Resource/Model/LeadExpertProfitsHandler'
import LeadIssueListHandler from '#/mock/handlers/Resource/List/Handler/Lead/LeadIssueListHandler'
import LeadPreviewHandler from '#/mock/handlers/Resource/Model/LeadPreviewHandler'
import NotifiableCSIssueListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Issue/NotifiableCSIssueListWithChatsHandler'
import NotifiableIssueListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Issue/NotifiableIssueListWithChatsHandler'
import NotifiableSaleListWithChatsHandler from '#/mock/handlers/Resource/List/Handler/Sale/NotifiableSaleListWithChatsHandler'
import PinnedAgentLeadsListHandler from '#/mock/handlers/Resource/List/Handler/Lead/PinnedAgentLeadsListHandler'
import ProductAdditionalInfoHandler from '#/mock/handlers/Resource/Model/ProductAdditionalInfoHandler'
import ReleasePostReleasePostItemListHandler from '#/mock/handlers/Resource/List/Handler/Release/ReleasePostReleasePostItemListHandler'
import ReleasesInfoHandler from '#/mock/handlers/Resource/Model/ReleasesInfoHandler'
import SaleBookkeepingInvoiceListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/SaleBookkeepingInvoiceListHandler'
import SaleDraftListHandler from '#/mock/handlers/Resource/List/Handler/Sale/SaleDraftListHandler'
import SaleInhouseBookkeepingInvoiceListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/SaleInhouseBookkeepingInvoiceListHandler'
import SaleInhouseBookkeepingTransactionAssignmentListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/SaleInhouseBookkeepingTransactionAssignmentListHandler'
import SaleOtherBookkeepingInvoiceListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/SaleOtherBookkeepingInvoiceListHandler'
import SaleOtherBookkeepingTransactionAssignmentListHandler from '#/mock/handlers/Resource/List/Handler/Sale/Bookkeeping/SaleOtherBookkeepingTransactionAssignmentListHandler'
import SalePreviewHandler from '#/mock/handlers/Resource/Model/SalePreviewHandler'
import SaleProjectCardListHandler from '#/mock/handlers/Resource/List/Handler/Sale/SaleProjectCardListHandler'
import SaleSaleTransactionListHandler from '#/mock/handlers/Resource/List/Handler/Sale/SaleSaleTransactionListHandler'
import SaleVersionSaleClientRequestListHandler from '#/mock/handlers/Resource/List/Handler/Sale/SaleVersionSaleClientRequestListHandler'
import SaleVersionSignedDocumentListHandler from '#/mock/handlers/Resource/List/Handler/SaleVersion/SaleVersionSignedDocumentListHandler'
import SaleVoucherListHandler from '#/mock/handlers/Resource/List/Handler/Sale/SaleVoucherListHandler'
import SubscribedCounterHandler from '#/mock/handlers/Resource/Model/SubscribedCounterHandler'
import TerminalHotkeyListHandler from '#/mock/handlers/Resource/List/Handler/TerminalHotkey/TerminalHotkeyListHandler'
import UnsubscribedCounterHandler from '#/mock/handlers/Resource/Model/UnsubscribedCounterHandler'

const handlers: { [Resource in ResourceName]?: MockResourceHandler } = {
    ...getGeneralResourceHandlers(),
    ActiveCSIssueListWithChats: ActiveCSIssueListWithChatsHandler,
    ActiveIssueListWithChats: ActiveIssueListWithChatsHandler,
    ActiveSaleListWithChats: ActiveSaleListWithChatsHandler,
    AgentAgentSkillList: AgentAgentSkillListHandler,
    AgentAppSettings: AgentAppSettingsHandler,
    AwardAccountAwardAccountIncomingRequestList: AwardAccountAwardAccountIncomingRequestListHandler,
    AwardAccountAwardAccountOutgoingRequestList: AwardAccountAwardAccountOutgoingRequestListHandler,
    BonusLead: BonusLeadHandler,
    BookkeepingInvoiceInhouseBookkeepingTransactionAssignmentList: BookkeepingInvoiceInhouseBookkeepingTransactionAssignmentListHandler,
    BookkeepingTransactionInhouseBookkeepingTransactionAssignmentList: BookkeepingTransactionInhouseBookkeepingTransactionAssignmentListHandler,
    ChatChatMessageList: ChatChatMessageListHandler,
    ChatChatMessagePinnedList: ChatChatMessagePinnedListHandler,
    ChatRecentList: ChatRecentListHandler,
    ClientClientEmailDataList: ClientClientEmailDataListHandler,
    ClientClientPhoneDataList: ClientClientPhoneDataListHandler,
    ClientPhoneCallContactAttemptList: ClientPhoneCallContactAttemptListHandler,
    ClientPreview: ClientPreviewHandler,
    ClientTravelersList: ClientTravelersListHandler,
    Counter: CounterHandler,
    DepartmentSkillList: DepartmentSkillListHandler,
    EmailEmailContactAttemptConversationList: EmailEmailContactAttemptConversationListHandler,
    ExternalUserAgentMilePriceProgramList: ExternalUserAgentMilePriceProgramListHandler,
    LeadAdditionalExpertQueueInformation: LeadAdditionalExpertQueueInformationHandler,
    LeadAdditionalFollowUp: LeadAdditionalFollowUpHandler,
    LeadExpertProfits: LeadExpertProfitsHandler,
    LeadIssueList: LeadIssueListHandler,
    LeadPreview: LeadPreviewHandler,
    NotifiableCSIssueListWithChats: NotifiableCSIssueListWithChatsHandler,
    NotifiableIssueListWithChats: NotifiableIssueListWithChatsHandler,
    NotifiableSaleListWithChats: NotifiableSaleListWithChatsHandler,
    PinnedAgentLeadsList: PinnedAgentLeadsListHandler,
    ProductAdditionalInfo: ProductAdditionalInfoHandler,
    ReleasePostReleasePostItemList: ReleasePostReleasePostItemListHandler,
    ReleasesInfo: ReleasesInfoHandler,
    SaleBookkeepingInvoiceList: SaleBookkeepingInvoiceListHandler,
    SaleDraftList: SaleDraftListHandler,
    SaleInhouseBookkeepingInvoiceList: SaleInhouseBookkeepingInvoiceListHandler,
    SaleInhouseBookkeepingTransactionAssignmentList: SaleInhouseBookkeepingTransactionAssignmentListHandler,
    SaleOtherBookkeepingInvoiceList: SaleOtherBookkeepingInvoiceListHandler,
    SaleOtherBookkeepingTransactionAssignmentList: SaleOtherBookkeepingTransactionAssignmentListHandler,
    SalePreview: SalePreviewHandler,
    SaleProjectCardList: SaleProjectCardListHandler,
    SaleSaleTransactionList: SaleSaleTransactionListHandler,
    SaleVersionSaleClientRequestList: SaleVersionSaleClientRequestListHandler,
    SaleVersionSignedDocumentList: SaleVersionSignedDocumentListHandler,
    SaleVoucherList: SaleVoucherListHandler,
    SubscribedCounter: SubscribedCounterHandler,
    TerminalHotkeyList: TerminalHotkeyListHandler,
    UnsubscribedCounter: UnsubscribedCounterHandler,
}

export default handlers