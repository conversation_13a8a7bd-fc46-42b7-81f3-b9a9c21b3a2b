import { define<PERSON><PERSON>l<PERSON>and<PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { ActivityLogCategory, ActivityLogType } from '~/api/models/Log/ActivityLog'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import AwardAccountFactory from '~mock/factories/AwardAccount/AwardAccountFactory'
import AwardAccountIncomingRequestFactory from '~mock/factories/AwardAccount/AwardAccountIncomingRequestFactory'
import { AwardAccountStatus } from '~/api/models/AwardAccount/AwardAccount'
import { pluckPks } from '~/lib/Helper/ArrayHelper'
import type { ModelAttributes } from '~types/lib/Model'

const modelName = 'AwardAccount'

export default defineModelHandler<typeof modelName>({
    actions: {
        changeStatus: async ({ pk, status }) => {
            await updateModelRecord(modelName, pk, {
                status,
            })
        },

        addRemark: async ({ pk, remark }) => {
            await createModelRecord('ActivityLog', {
                created_at: dateToUnixTimestamp(new Date()),
                created_by_pk: useMockAuth().pk,
                model_name: modelName,
                model_pk: pk,
                type: ActivityLogType.Text,
                category: ActivityLogCategory.Remark,
                data: {
                    text: remark,
                },
            })
        },

        create: async ({ data }) => {
            await (new AwardAccountFactory()).create(data)
        },

        edit: async ({ pk, data }) => {
            await updateModelRecord(modelName, pk, {
                ...data,
            })
            await updateModelRecord('AwardAccountAdditionalCredentials', pk, {
                password: data.password,
                email: data.email,
                email_password: data.email_password,
                phone: data.phone,
                street: data.street,
                city: data.city,
                state: data.state,
                zip: data.zip,
                country: data.country,
            })
        },

        delete: async ({ pk }) => {
            await updateModelRecord(modelName, pk, {
                status: AwardAccountStatus.Lost,
            })
        },

        addMiles: async function({ pk, data }) {
            const account = await findModelRecordOrFail('AwardAccount', pk)

            const consolidator_pk = data.consolidator_pk || account.consolidator_pk

            if (!consolidator_pk) {
                throw new Error('Consolidator is not set')
            }

            await (new AwardAccountIncomingRequestFactory()).create({
                award_account_pk: pk,
                consolidator_pk,
                miles_amount: data.amount,
                remark: data.remark,
                price: data.amount * data.cpm,
            })
        },

        moveMiles: async function(data) {
            const fromAccount = await findModelRecordOrFail('AwardAccount', data.from_pk)
            const toAccount = await findModelRecordOrFail('AwardAccount', data.to_pk)
            const milesTax = data.vcc_card_pk ? 0 : data.fee_amount

            await updateModelRecord('AwardAccount', data.from_pk, {
                balance: fromAccount.balance - data.miles_amount - milesTax,
            })

            await updateModelRecord('AwardAccount', data.to_pk, {
                balance: toAccount.balance + data.miles_amount,
            })
        },

        pay: async ({ pk, tickets }) => {
            for (const ticketPk of tickets) {
                const ticket = await findModelRecordOrFail('Ticket', ticketPk)

                if (!ticket || !ticket.product_pk) {
                    break
                }

                const product = await findModelRecordOrFail('Product', ticket.product_pk)

                if (product && product.is_award) {
                    await createModelRecord('AwardAccountOutgoingRequest', {
                        award_account_pk: pk,
                        miles_amount: product.fare,
                        product_pk: ticket.product_pk,
                        remark: product?.remark || '',
                        created_at: dateToUnixTimestamp(new Date()),
                        is_blocked: false,
                        price: product.fare * product.net_price,
                        is_refund: false,
                    })
                    await updateModelRecord('Ticket', ticketPk, { award_account_pk: pk })
                }
            }
        },

        clearOutgoingRequests: async function() {
            // todo
        },

        accountsMatchForSale: async function() {
            const accounts = await findModelRecords(modelName, (account) => {
                return [AwardAccountStatus.InUse, AwardAccountStatus.Active].includes(account.status)
            })

            return this.response(
                pluckPks(accounts).map(pk => ({
                    pk,
                    client: randomBoolean(),
                    holder: randomBoolean(),
                })),
            )
        },

        rejectSale: async ({ pk }) => {
            await updateModelRecord(modelName, pk, {
                status: AwardAccountStatus.Active,
            })
        },

        setInitialBookingState: async (data) => {
            await updateModelRecord(modelName, data.pk, {
                in_use_till_date: data.in_use_till_date,

                active_sale_count: data.active_sale_count,
                refund_sale_count: data.refund_sale_count,
                exchange_sale_count: data.exchange_sale_count,
                top_up_count: data.top_up_count,
            })
        },

        refundTicket: async function() {
            // todo
        },

        getMilesMaxValue: async function() {
            return this.response(9999)
        },

        getSummary: async function() {
            const awardAccountList = await findModelRecords('AwardAccount')

            return this.response({
                points: 0,
                account_count: 1,
            })
        },
    },
    searchFields: {
        keywords: async (record) => {
            return record.account_number
        },
        group_account_number: async (record: ModelAttributes<'AwardAccount'>) => {
            if (record.group_pk) {
                return (await findModelRecordOrFail('AwardAccount', record.group_pk)).account_number
            }

            return record.account_number
        },
        alt_vpn: async (record: ModelAttributes<'AwardAccount'>) => {
            return record.external_user_agent_pk ? '1' : '0'
        },
    },
})

const calculateWeightedPrice = (gooddies: Array<{
    amount: number
    price: number
}>) => {
    let sumOfTotals = 0
    let sumOfShares = 0

    for (const order of gooddies) {
        sumOfTotals = sumOfTotals + (order.price * order.amount)
        sumOfShares = sumOfShares + order.amount
    }

    return {
        price: sumOfTotals / sumOfShares,
        amount: sumOfShares,
    }
}

export const recalculateAccountBalance = async (awardAccountPk: PrimaryKey) => {
    const requests = await findModelRecords('AwardAccountIncomingRequest', { award_account_pk: awardAccountPk })

    const goodies = requests.map(goodie => {
        const cpm = goodie.miles_amount / goodie.price

        return {
            amount: goodie.balance,
            price: cpm * goodie.balance,
        }
    }).filter(goodie => goodie.amount > 0)

    if (goodies?.length) {
        const weightedRequest = calculateWeightedPrice(goodies)
        await updateModelRecord('AwardAccount', awardAccountPk, {
            balance: weightedRequest.amount,
            cpm: parseFloat((weightedRequest.price / weightedRequest.amount).toFixed(4)),
        })
    }
}
