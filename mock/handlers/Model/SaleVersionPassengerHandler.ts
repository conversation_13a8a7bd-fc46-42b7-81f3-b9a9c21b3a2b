import { defineModelHand<PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'SaleVersionPassenger'

export default defineModelHandler<typeof modelName>({
    actions: {
        update: async function({ pk, data }) {
            const item = await updateModelRecord(modelName, pk, {
                ...data,
            })

            return this.response(item)
        },
        //
    },
})
