import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
createModelRecord,
deleteModelRecord,
findModelRecord,
findModelRecords,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { ExternalUserAgentStatus } from '~/api/models/ExternalUserAgent/ExternalUserAgent'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { usePk } from '~/composables/usePk'
import { pluckPks, randomElement } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { randomInt } from '~/lib/Helper/NumberHelper'
import ExternalUserAgentProxyFactory from '~mock/factories/ExternalUserAgent/ExternalUserAgentProxyFactory'

const modelName = 'ExternalUserAgent'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ award_account_pk, name, note, advanced_settings, proxy }) {
            const init_advanced_settings = {
                user_agent: null,
                screen_resolution_custom: null,
                hardware_concurrency: null,
                oscpu: null,
                platform: null,
                video_input_count: null,
                whitelisted_ports: null,

                screen_masking: 'mask',

                media_devices_masking: 'mask',
                audio_input_count: 1,
                audio_output_count: 1,

                graphics_noise: 'mask',
                graphics_masking: 'noise',
                canvas_noise: 'mask',
                audio_masking: 'natural',

                navigator_masking: 'mask',

                ports_masking: 'mask',

                fonts_masking: 'mask',
                browser_type: 'stealthfox',
            }

            const externalUserAgent = await createModelRecord('ExternalUserAgent', {
                status: ExternalUserAgentStatus.Approved,
                name,
                note,
                profile_id: 'qweqwe',
                folder_id: 'qwe',
                created_by_pk: useMockAuth().pk,
                created_at: dateToUnixTimestamp(new Date()),
                last_opened_at: null,
                last_opened_by_pk: null,
                advanced_settings: {
                    ...init_advanced_settings,
                    ...advanced_settings,
                },
            })

            if (proxy) {
                const externalUserAgentProxy = await createModelRecord('ExternalUserAgentProxy', {
                    type: proxy.type,
                    location: proxy.location ?? {
                        country: 'us',
                        city: null,
                        region: null,
                    },
                    credential: proxy.credential,
                })

                await updateModelRecord('ExternalUserAgent', usePk(externalUserAgent), {
                    external_user_agent_proxy_pk: usePk(externalUserAgentProxy),
                })
            }

            if (award_account_pk) {
                await updateModelRecord('AwardAccount', award_account_pk, { external_user_agent_pk: usePk(externalUserAgent) })
            }

            return this.response({
                pk: usePk(externalUserAgent),
            })
        },

        save: async function({ pk, name, note, advanced_settings, proxy }) {
            await updateModelRecord('ExternalUserAgent', pk, {
                name,
                note,
                advanced_settings,
                proxy,
            })
        },

        createFromUrl: async function({ url }) {
            const countries = (await findModelRecords('Country')).map((country) => country.code2)

            const externalUserAgent = await createModelRecord('ExternalUserAgent', {
                status: ExternalUserAgentStatus.Pending,
                name: faker.word.adjective(),
                note: faker.word.words(10),
                profile_id: '5d5a042f-0d8a-4fda-aaed-238dbdb051d0', // faker.word.adjective(),
                folder_id: 'c6d1a576-649c-4725-9aed-292430b2f575', // faker.word.adjective(),
                created_by_pk: useMockAuth().pk,
                created_at: dateToUnixTimestamp(new Date()),
                last_opened_at: null,
                last_opened_by_pk: null,
                advanced_settings: {
                    user_agent: null,
                    screen_resolution_custom: null,
                    hardware_concurrency: null,
                    oscpu: null,
                    platform: null,
                    video_input_count: null,
                    whitelisted_ports: null,

                    screen_masking: 'mask',

                    media_devices_masking: 'mask',
                    audio_input_count: 1,
                    audio_output_count: 1,

                    graphics_noise: 'mask',
                    graphics_masking: 'mask',
                    canvas_noise: 'mask',
                    audio_masking: 'noise',

                    navigator_masking: 'mask',

                    ports_masking: 'mask',

                    fonts_masking: 'mask',
                    browser_type: 'stealthfox',
                },
            })

            return this.response({
                pk: usePk(externalUserAgent),
            })
        },

        linkWithAwardAccount: async function({ award_account_pk, external_user_agent_pk }) {
            // @TODO: update resource list 'ExternalUserAgentMilePriceProgramList'
        },

        remove: async function({ pk }) {
            const awardAccount = await findModelRecord('AwardAccount', { external_user_agent_pk: pk })

            if (awardAccount) {
                await updateModelRecord('AwardAccount', usePk(awardAccount), { external_user_agent_pk: null })
            }
            await deleteModelRecord('ExternalUserAgent', pk)
        },

        getMultiLoginToken: async function({}) {
            return this.response({
                token: 'eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.u_YTVgG_wIM9M66dfYrS61YUUWOXrZHUtbwEGAGyp6XnxyXe3QMRrsFnKHtflbfgMaOJeQ4o0w8VcTcm6O1SYg',
            })
        },

        getMultiLoginFormOptions: async function({}) {
            const screen_resolutions = [
                { title: '1280x720', value: '1280x720' },
                { title: '1440x900', value: '1440x900' },
                { title: '1600x900', value: '1600x900' },
                { title: '1920x1080', value: '1920x1080' },
                { title: '1920x1200', value: '1920x1200' },
                { title: '2560x1440', value: '2560x1440' },
                { title: '2560x1600', value: '2560x1600' },
                { title: '3840x2160', value: '3840x2160' },
                { title: '5120x2880', value: '5120x2880' },
            ]

            const hardware_concurrencies = []

            for (let i = 2; i <= 16; i += 2) {
                hardware_concurrencies.push({ title: String(i), value: String(i) })
            }

            return this.response({
                browsers: [
                    { title: 'Mimic X', value: 'mimicx' },
                    { title: 'Stealthfox X', value: 'stealthfox' },
                ],
                screen_resolutions,
                hardware_concurrencies,
            })
        },

        getMultiLoginQuickProfileFormOptions: async function({}) {
            return this.response({
                os_types: [
                    { title: 'macOS', value: 'macos' },
                    { title: 'Windows', value: 'windows' },
                    { title: 'Android', value: 'android' },
                    { title: 'Linux', value: 'linux' },
                ],
                browser_types: [
                    { title: 'Mimic X', value: 'mimic' },
                    { title: 'Stealthfox X', value: 'stealthfox' },
                ],
            })
        },

        // getQuickProfileLog: async function({}) {
        //     const logs = Array.from({ length: randomInt(3, 10) }, () => ({
        //         created_by_pk: useMockAuth().pk,
        //         created_at: dateToUnixTimestamp(faker.date.past()),
        //         name: faker.word.adjective(),
        //         proxy: faker.datatype.boolean() ? {
        //             username: faker.datatype.boolean() ? faker.internet.userName() : null,
        //             password: faker.datatype.boolean() ? faker.internet.password() : null,
        //             host: faker.internet.ip(),
        //             port: faker.internet.port(),
        //             protocol: randomElement(['http', 'https', 'socks5']),
        //         } : null,
        //         browser_type: randomElement(['mimic', 'stealthfox']),
        //         os_type: randomElement(['macos', 'windows', 'android', 'linux']),
        //         custom_start_urls: Array.from({ length: randomInt(0, 3) }, () => faker.internet.url()),
        //     }))
        //
        //     return this.response(logs)
        // },

        getNotUsedExternalUserAgentPks: async function({ mile_price_program_pk }) {
            const externalUserAgents = await findModelRecords('ExternalUserAgent')

            return this.response({ pks: pluckPks(externalUserAgents) })
        },

        updateIsApproval: async function({}) {

        },
    },
})
