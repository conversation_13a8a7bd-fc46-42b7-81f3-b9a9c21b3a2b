import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import PnrScheduleFactory from '~mock/factories/PnrSchedule/PnrScheduleFactory'
import { findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'PnrSchedule'

export default defineModelHandler<typeof modelName>({
    actions: {
        updatePNRCheck: async function({ pk }) {
            const current = await findModelRecord('PnrSchedule', pk)

            const new_pnr = await (new PnrScheduleFactory().make({
                sale_pk: current?.sale_pk,
            }))

            await updateModelRecord('PnrSchedule', pk, {
                updated_at: dateToUnixTimestamp(new Date()),
                original_itinerary: new_pnr.original_itinerary,
                updated_itinerary: new_pnr.updated_itinerary,
            })
        },

        getSaleScheduleInfo: async function({ sale_pk }) {
            return this.response({
                tickets: [
                    { pnr: 'aswfwgj', consolidator_area_pk: '1' },
                    { pnr: 'aggfghf', consolidator_area_pk: '2' },
                ],
                original_itinerary: '1 UA  970 27JUN ORD FCO  350P  755A¥1 D    781  9.05  4823  N\n' +
                    '     DEP-TERMINAL 1                 ARR-TERMINAL 3                 \n' +
                    '     CABIN-BUSINESS\n' +
                    '\n' +
                    '2 UA  971 09JUL FCO ORD  945A  105P   L    781 10.20  4823  N\n' +
                    '     DEP-TERMINAL 3                 ARR-INTL TERMINAL 5            \n' +
                    '     CABIN-BUSINESS.\n' +
                    '\n' +
                    '*IA«\n' +
                    '\n' +
                    '1 UA 970P 27JUN T ORDFCO GK5   350P  755A  28JUN W /E\n' +
                    '2 UA 971P 09JUL S FCOORD GK5   945A  105P /E',
            })
        },

        createPnrSchedule: async function({ pnr, sale_pk, consolidator_area_pk, type, original_itinerary, updated_itinerary }) {
            await (new PnrScheduleFactory().create({
                pnr,
                sale_pk,
                consolidator_area_pk,
                type,
            }, { withUpdated: true }))
        },
    },

    searchFields: {
        has_issue: async (record) => {
            return !!record.issue_pk ? 1 : 0
        },
        has_updated_itinerary: async (record) => {
            return record.updated_itinerary && record.updated_itinerary.length > 0 ? 1 : 0
        },
    },
})
