import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    deleteModelRecords,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
    wherePk,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { composeResourcePk, usePk } from '~/composables/usePk'
import { useMockAuth } from '~mock/composables/useMockAuth'

const modelName = 'Chat'

export default defineModelHandler<typeof modelName>({
    actions: {
        setLastReadAt: async function({ pk, last_read_at }) {
            const messages = await findModelRecords('ChatMessage', {
                chat_pk: pk,
            })

            const infoPk = composeResourcePk('ChatAdditionalInfo', {
                pk: pk,
                auth_pk: useMockAuth().pk,
            })

            const info = await findModelRecordOrFail('ChatAdditionalInfo', wherePk(infoPk))

            await updateModelRecord('ChatAdditionalInfo', infoPk, {
                messages_count: messages.length,
                new_messages_count: !info?.last_read_at ?
                    messages.length :
                    messages.filter((message) => message.created_at > (info.last_read_at || 0)).length,
                last_message_pk: tryUsePk(messages.at(-1)),
                last_read_at: Math.max(info?.last_read_at || 0, last_read_at || 0) || null,
            })
        },

        restoreFromArchive: async function({ pk }) {
            await updateModelRecord('Chat', pk, {
                is_archived: false,
            })

            return this.response(await findModelRecordOrFail('Chat', pk))
        },
    },

    observers: {
        afterDelete: async function(chat) {
            const pk = usePk(chat)

            await deleteModelRecords('ChatGroup', {
                chat_pk: pk,
            })

            await deleteModelRecords('ChatMessage', {
                chat_pk: pk,
            })

            // @todo On delete cascade
            await deleteModelRecords('ChatAdditionalInfo', wherePk(pk))
        },
    },
})
