import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'

import { createModelRecord, deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'AirlineCase'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ company_contact_pk, issue_pk, airline_pk, case_number, remark }) {
            await createModelRecord(modelName, {
                company_contact_pk,
                issue_pk,
                airline_pk,
                case_number,
                remark,
                created_at: Date.now(),
            })
        },
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },

        update: async function({ pk, company_contact_pk, airline_pk, case_number, remark }) {
            await updateModelRecord(modelName, pk, {
                company_contact_pk,
                airline_pk,
                case_number,
                remark,
            })
        },
    },
    searchFields: {
        case_number: async (record: any) => {
            return record.case_number
        },
    },
})
