import { define<PERSON><PERSON>l<PERSON>and<PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'Ticket'

export default defineModelHandler<typeof modelName>({
    actions: {
        //
    },
    searchFields: {
        is_award: async (record) => {
            const product = await findModelRecordOrFail('Product', record.product_pk)

            return product?.net_price_currency_pk === '2'
        },
        sale_pk: async (record) => {
            const sales = await findModelRecords('Sale')

            return randomElementPk(sales) === '10'
        },
        only_active_version: async (record) => {
            return true
        },
    },

})
