import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import type { ModelAttributes } from '~types/lib/Model'
import { findModelRecord, findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { wait } from '~/lib/Helper/PromiseHelper'

export default defineModelHandler<'CallContactAttempt'>({
    actions: {
        generateShortSummary: async function({ pk }) {
            const callAttempt  = await findModelRecord('CallContactAttempt', pk)

            if (callAttempt?.transcription_pk) {
                const summary = faker.lorem.paragraph({ min: 4, max: 6 }).slice(0, 512)

                await updateModelRecord('AudioTranscription', callAttempt.transcription_pk, {
                    short_summary: summary,
                })

                return this.response({
                    short_summary: summary,
                })
            }

            return this.response({
                short_summary: '',
            })
        },

        generateGeneralSummary: async function(params) {
            await wait(3000)

            const calls = await findModelRecords('CallContactAttempt')

            const pk = randomElementPk(calls)

            const summary = faker.lorem.paragraph({ min: 4, max: 6 }).slice(0, 512)

            await updateModelRecord('CallContactAttempt', pk, {
                general_summary: summary,
            })

            return this.response({
                general_summary: summary,
            })
        },
    },
    searchFields: {
        id(record: ModelAttributes<'CallContactAttempt'>) {
            return record.id
        },
        created_at(record: ModelAttributes<'CallContactAttempt'>) {
            return record.created_at
        },
        agent_name: async (record: ModelAttributes<'CallContactAttempt'>) => {
            const agent = await findModelRecord('Agent', { id: Number(record.agent_pk) })

            return `${agent?.first_name} ${agent?.last_name}` ?? ''
        },
        agent_phone: async (record: ModelAttributes<'CallContactAttempt'>) => {
            const agent = await findModelRecord('Agent', { id: Number(record.agent_pk) })

            return agent?.phone ?? ''
        },
        client_phone(record: ModelAttributes<'CallContactAttempt'>) {
            return record.client_phone
        },
        is_on_call_management_page() {
            return true
        },
    },
})
