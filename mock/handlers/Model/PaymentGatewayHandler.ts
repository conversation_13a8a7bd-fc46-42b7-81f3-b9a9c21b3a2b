import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'PaymentGateway'

export default defineModelHandler<typeof modelName>({
    actions: {
        resetAmounts: async function({ gateway_pks }) {
            for (const pk of gateway_pks) {
                await updateModelRecord('PaymentGatewayBalanceAdditional', pk, {
                    amount: 0,
                    amount_ps: 0,
                })
            }
        },
        updateTarget: async function({ gateways }) {
            for (const gateway of gateways) {
                await updateModelRecord('PaymentGatewayBalanceAdditional', gateway.pk, {
                    target_ps: gateway.target_ps,
                    limit_target: gateway.limit_target,
                })
            }
        },

        getPaymentGatewaysForSale: async function({ sale_pk }) {
            const gateways = await findModelRecords('PaymentGateway')

            return this.response(gateways.map(gateway => {
                return {
                    id: gateway.id,
                    name: gateway.name,
                    system_name: gateway.system_name,
                    is_default: gateway.is_default,
                }
            }))
        },
    },
})
