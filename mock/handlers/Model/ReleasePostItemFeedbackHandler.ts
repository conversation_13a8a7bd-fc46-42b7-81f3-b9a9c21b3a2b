import ReleasePostItemFeedbackFactory from '~mock/factories/ReleasePost/ReleasePostItemFeedbackFactory'
const modelName = 'ReleasePostItemFeedback'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ points, release_post_item_pk, remark }) {
            await new ReleasePostItemFeedbackFactory().create({ points, release_post_item_pk, remark })
        },
    },
})
