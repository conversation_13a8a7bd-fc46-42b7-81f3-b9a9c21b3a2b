import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import AirlineReportFactory from '~mock/factories/AirlineReport/AirlineReportFactory'
import { usePk } from '~/composables/usePk'
import { makeReportAssignments } from '~mock/seeds/models/AirlineReport/AirlineReportSeeder'
import AirlineReportVersionFactory from '~mock/factories/AirlineReport/AirlineReportVersionFactory'
import { deleteModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

export default defineModelHandler<'AirlineReport'>({
    actions: {
        create: async function({ data }) {
            const report = await (new AirlineReportFactory()).create(data)

            const { rows, amount } = await makeReportAssignments(30)

            await updateModelRecord('AirlineReport', usePk(report), {
                amount,
            })

            await (new AirlineReportVersionFactory()).create({
                rows,
                report_pk: usePk(report),
            })

            return this.response(report)
        },

        setExecutor: async function({ pk, executor_pk }) {
            await updateModelRecord('AirlineReport', pk, {
                executor_pk,
                executor_last_activity: dateToUnixTimestamp(new Date()),
            })
        },
    },

    searchFields: {
        has_executor(record: ModelAttributes<'AirlineReport'>) {
            return !!record.executor_pk
        },
    },

    observers: {
        async afterDelete(record: ModelAttributes<'AirlineReport'>) {
            const pk = usePk(record)

            await deleteModelRecords('AirlineReportVersion', {
                report_pk: pk,
            })
        },
    },
})
