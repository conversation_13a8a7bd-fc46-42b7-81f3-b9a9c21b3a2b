import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

export default defineModelHandler<'AgentSkill'>({
    actions: {
        update: async function({ pk, skill_level }) {
            const agentSkill = await findModelRecordOrFail('AgentSkill', pk)

            await updateModelRecord('AgentSkill', usePk(agentSkill), { skill_level: skill_level })
        },
    },
})
