import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import AirlineReportVersionFactory from '~mock/factories/AirlineReport/AirlineReportVersionFactory'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { AirlineReportStage } from '~/api/models/AirlineReport/AirlineReportVersion'
import SaleExtraProfitFactory from '~mock/factories/Sale/SaleExtraProfitFactory'

export default defineModelHandler<'AirlineReportVersion'>({
    actions: {
        getState: async function({ pk }) {
            const version = await findModelRecordOrFail('AirlineReportVersion', pk)

            return this.response({
                rows: version.rows,
                stage: version.stage,
                page: version.page,
                version: version.version,
            })
        },

        save: async function({ report_pk, state, is_autosave, go_to_next_stage }) {
            let version = await (new AirlineReportVersionFactory()).create({
                report_pk,
                version: state.version,
                rows: state.rows,
                page: state.page,
                stage: state.stage,
                is_autosave,
                created_by_pk: useMockAuth().pk,
                created_at: dateToUnixTimestamp(new Date()),
            })

            if (go_to_next_stage) {
                version = await (new AirlineReportVersionFactory()).create({
                    report_pk,
                    version: state.version,
                    rows: state.rows,
                    page: state.page,
                    stage: getNextStage(version.stage),
                    is_autosave,
                    created_by_pk: useMockAuth().pk,
                    created_at: dateToUnixTimestamp(new Date()),
                })
            }

            const versionPk = usePk(version)

            await setActiveAirlineReportVersion(versionPk)

            if (version.stage === AirlineReportStage.Completed) {
                for (const row of version.rows) {
                    for (const assignment of row.assignments) {
                        if (assignment.is_extra) {
                            await (new SaleExtraProfitFactory()).create({
                                airline_report_pk: report_pk,
                                amount: assignment.amount,
                                product_pk: assignment.product_pk,
                                sale_pk: row.sale_pk,
                                field_type: assignment.field_type,
                                remark: row.remark,
                            })
                        }

                        // TODO: voucher
                    }
                }
            }

            return this.response({
                version_pk: versionPk,
            })
        },
    },
})

async function setActiveAirlineReportVersion(versionPk: PrimaryKey) {
    const version = await findModelRecordOrFail('AirlineReportVersion', versionPk)

    const report = await findModelRecordOrFail('AirlineReport', version.report_pk)

    await updateModelRecord('AirlineReport', usePk(report), {
        active_version_pk: versionPk,
        stage: version.stage,
    })
}

function getNextStage(stage: AirlineReportStage) {
    if (stage === AirlineReportStage.Rows) {
        return AirlineReportStage.Assignments
    } else if (stage === AirlineReportStage.Assignments) {
        return AirlineReportStage.Completed
    } else {
        return AirlineReportStage.ClosedWithProblem
    }
}
