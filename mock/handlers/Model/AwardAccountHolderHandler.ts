import { defineModelHand<PERSON> } from '~mock/utils/define'
import { findModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'AwardAccountHolder'

export default defineModelHandler<typeof modelName>({
    actions: {
        //
    },

    searchFields: {
        keywords: async (record) => {
            const awardAccount = await findModelRecord('AwardAccount', record.award_account_pk)

            return awardAccount?.account_number
        },
    },
})
