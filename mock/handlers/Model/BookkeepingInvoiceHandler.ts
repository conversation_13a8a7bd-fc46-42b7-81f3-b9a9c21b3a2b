import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    deleteModelRecords,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'
import BookkeepingInvoiceFactory from '~mock/factories/Bookkeeping/Invoice/BookkeepingInvoiceFactory'
import { similarity } from '~/lib/Helper/StringHelper'

export default defineModelHandler<'BookkeepingInvoice'>({
    actions: {
        createForSale: async function() {
            throw new Error('Not implemented') // @todo
        },

        pay: async function() {
            throw new Error('Not implemented') // @todo
        },

        create: async function({ invoice }) {
            await (new BookkeepingInvoiceFactory()).create(invoice)
        },

        update: async function({ pk, invoice }) {
            await updateModelRecord('BookkeepingInvoice', pk, invoice)
        },

        updateDescription: async function({ pk, description }) {
            await updateModelRecord('BookkeepingInvoice', pk, {
                description,
            })
        },

        matchWithTransaction: async function({ transaction_pk }) {
            const transaction = await findModelRecord('BookkeepingTransaction', transaction_pk)

            const invoices = await findModelRecords('BookkeepingInvoice', (record) => {
                return (!!record?.description && !!transaction?.description) && similarity(record.description, transaction?.description) > 0.05
            })

            return this.response(invoices)
        },
    },

    observers: {
        afterDelete: async function(record) {
            const pk = usePk(record)

            await deleteModelRecords('ActivityLog', {
                model_name: 'BookkeepingInvoice',
                model_pk: pk,
            })

            await deleteModelRecords('BookkeepingInvoiceDocument', {
                invoice_pk: pk,
            })

            await deleteModelRecords('BookkeepingTransactionAssignment', {
                invoice_pk: pk,
            })
        },
    },
})

export const recalculateInvoiceBalance = async (invoice_pk: string) => {
    const invoice = await findModelRecord('BookkeepingInvoice', { id: Number(invoice_pk) })

    const assignments = await findModelRecords('BookkeepingTransactionAssignment', { invoice_pk })

    if (invoice && assignments.length) {
        let balance = invoice.amount * Number(invoice.direction)
        assignments.forEach(assignment => {
            balance += assignment.amount
        })

        await updateModelRecord('BookkeepingInvoice', usePk(invoice), {
            balance: balance,
        })
    }
}
