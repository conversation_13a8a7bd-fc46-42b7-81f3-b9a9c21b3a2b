import { defineModel<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    nextIncrementValue,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'Skill'

export default defineModelHandler<typeof modelName>({
    actions: {
        delete: async ({ pk }) => {
            await deleteModelRecord(modelName, pk)
        },
        add: async ({ name, department_pk }) => {
            await createModelRecord(modelName, {
                name,
                department_pk,
                id: nextIncrementValue(modelName),
            })
        },
        update: async ({ pk, name }) => {
            await updateModelRecord(modelName, pk, { name })
        },
    },
})
