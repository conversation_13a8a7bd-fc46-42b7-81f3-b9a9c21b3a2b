import { define<PERSON>odelHand<PERSON> } from '~mock/utils/define'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

const modelName = 'PriceDropCheck'

export default defineModelHandler<typeof modelName>({
    actions: {
        //
    },
    searchFields: {
        //
        has_offers: async (record) => {
            const offers = await findModelRecords('PriceDropOffer', {
                check_pk: usePk(record),
            })

            return offers.length > 0
        },
    },
})
