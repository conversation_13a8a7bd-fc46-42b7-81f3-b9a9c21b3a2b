import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { recalculateInvoiceBalance } from '~mock/handlers/Model/BookkeepingInvoiceHandler'
import { recalculateTransactionBalance } from '~mock/handlers/Model/BookkeepingTransactionHandler'
import type { ModelAttributes } from '~types/lib/Model'

export default defineModelHandler<'BookkeepingTransactionAssignment'>({
    actions: {
        create: async function({ assignment }) {
            await createModelRecord('BookkeepingTransactionAssignment', {
                ...assignment,
                created_at: Math.floor(Date.now() / 1000),
                created_by_pk: randomElementPk(await findModelRecords('Agent')),
            })
        },

        update: async function({ pk, assignment }) {
            await updateModelRecord('BookkeepingTransactionAssignment', pk, {
                ...assignment,
                created_at: Math.floor(Date.now() / 1000),
                created_by_pk: randomElementPk(await findModelRecords('Agent')),
            })
        },

        delete: async function({ pk }) {
            const assignment = await findModelRecord('BookkeepingTransactionAssignment', pk)

            if (assignment) {
                await recalcRelations(assignment)

                await deleteModelRecord('BookkeepingTransactionAssignment', pk)
            }
        },

        refund: async function({ balances }) {
            // @todo implement refund
        },
    },
    observers: {
        afterCreate: async function(record) {
            await recalcRelations(record)
        },
        afterUpdate: async function(record) {
            await recalcRelations(record)
        },
    },

})

const recalcRelations = async (assignment: ModelAttributes<'BookkeepingTransactionAssignment'>) => {
    if (assignment.invoice_pk) {
        await recalculateInvoiceBalance(assignment.invoice_pk)
    }

    if (assignment.transaction_pk) {
        await recalculateTransactionBalance(assignment.transaction_pk)
    }
}

