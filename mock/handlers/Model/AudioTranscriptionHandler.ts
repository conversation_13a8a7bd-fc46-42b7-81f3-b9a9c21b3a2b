import { defineModelHand<PERSON> } from '~mock/utils/define'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'AudioTranscription'

export default defineModelHandler<typeof modelName>({
    actions: {
       loadTextFromVoiceFile: async function({ file_pk }) {
           const transcriptions = await findModelRecords('AudioTranscription')
           const pk = randomElementPk(transcriptions)

           return this.response({ pk })
       },
    },
})
