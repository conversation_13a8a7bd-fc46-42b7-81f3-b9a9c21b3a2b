import {
    createModelRecord,
    deleteModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { ReleasePostStatus } from '~/api/models/ReleasePost/ReleasePost'
import { faker } from '@faker-js/faker'

const modelName = 'ReleasePost'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ title, description }) {
            await createModelRecord(modelName, {
                title: title,
                created_at: Date.now(),
                description: description,
                status: ReleasePostStatus.New,
            })
        },
        update: async function({ pk, title, description }) {
            await updateModelRecord(modelName, pk, {
                title, description,
            })
        },
        remove: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },
        sendForApproval: async function({ pk }) {
            await updateModelRecord(modelName, pk, {
                status: ReleasePostStatus.WaitingForApproval,
            })
        },
        reject: async function({ pk }) {
            await updateModelRecord(modelName, pk, {
                status: ReleasePostStatus.Rejected,
            })
        },
        approve: async function({ pk }) {
            await updateModelRecord(modelName, pk, {
                status: ReleasePostStatus.Approved,
            })
        },
        publish: async function({ pk }) {
            await updateModelRecord(modelName, pk, {
                status: ReleasePostStatus.Published,
            })
        },

        getAgentsThatHaveAccessToRelease: async function({ pk }) {
            const agents = (await findModelRecords('Agent')).slice(1, 10)

            return this.response(agents.map((agent) => {
                return {
                    pk: usePk(agent),
                    viewed_at: faker.date.anytime().getTime(),
                }
            }))
        },

        setViewedReleasePost: async function(data) {
            //
        },
    },
})
