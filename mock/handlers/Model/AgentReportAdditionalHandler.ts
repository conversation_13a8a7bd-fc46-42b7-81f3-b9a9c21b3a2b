import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import AgentReportAdditionalFactory from '~mock/factories/AgentReport/AgentReportAdditionalFactory'

export default defineModelHandler<'AgentReportAdditional'>({
    actions: {

        save: async function({ invoice_pk, data }) {
            for (const assignment of data) {
                await updateModelRecord('AgentReportAdditional', assignment.pk, assignment)
            }
        },

        delete: async function({ pk }) {
            await deleteModelRecord('AgentReportAdditional', pk)
        },

        addAdditional: async function({ data }) {
            await new AgentReportAdditionalFactory().create(data)
        },
    },

    searchFields: {},

    observers: {},
})
