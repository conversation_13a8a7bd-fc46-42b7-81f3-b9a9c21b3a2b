import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    wherePk,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { AgentClientTransferState } from '~/api/models/Agent/AgentClientTransfer'
import { AgentClientTransferInfoStatus } from '~/api/models/Agent/AgentClientTransfer'
import type z from 'zod'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'

export default defineModelHandler<'AgentClientTransfer'>({
    actions: {
        createTransfer: async function({ agent_pk, type }) {
            const transferRecord = await createModelRecord('AgentClientTransfer', { agent_pk: agent_pk, type: type, status: AgentClientTransferInfoStatus.New })

            return this.response({
                transfer_pk: usePk(transferRecord),
            })
        },

        getTransferState: async function({ transfer_pk }) {
            await findModelRecordOrFail('AgentClientTransfer', transfer_pk)

            // Some state unrelated to transfer type
            const clients = await findModelRecords('Client')

            const state: z.infer<typeof AgentClientTransferState> = clients.map((client) => {
                if (!client.curator_pk) {
                    return
                }

                return {
                    client_pk: usePk(client),
                    from_agent_pk: client.curator_pk,
                    to_agent_pk: null,
                    is_fixed: client.is_fixed,
                }
            }).filter(Boolean)

            return this.response({
                state,
            })
        },

        unassignClients: async function({ selected_client_pks }) {
            const clients = await findModelRecords('Client', wherePk(selected_client_pks))

            const updates: z.infer<typeof AgentClientTransferState> = clients.map(client => {
                if (!client.curator_pk) {
                    return
                }

                return {
                    client_pk: usePk(client),
                    from_agent_pk: client.curator_pk,
                    to_agent_pk: null,
                    is_fixed: client.is_fixed,
                }
            }).filter(Boolean)

            return this.response({
                state_updates: updates,
            })
        },

        completeTransfer: async function() {
            //
        },

        assignClientsToTeam: async function(data) {
            const clients = await findModelRecords('Client', wherePk(data.selected_client_pks))
            const agentsInTeam = await findModelRecords('Agent', {
                team_pk: data.team_pk,
            })

            const updates: z.infer<typeof AgentClientTransferState> = clients.map(client => {
                if (!client.curator_pk) {
                    return
                }

                return {
                    client_pk: usePk(client),
                    from_agent_pk: client.curator_pk,
                    to_agent_pk: randomElementPk(agentsInTeam),
                    is_fixed: client.is_fixed,
                }
            }).filter(Boolean)

            return this.response({
                state_updates: updates,
            })
        },

        assignClientsToAgent: async function(data) {
            const clients = await findModelRecords('Client', wherePk(data.selected_client_pks))

            const updates: z.infer<typeof AgentClientTransferState> = clients.map(client => {
                if (!client.curator_pk) {
                    return
                }

                return {
                    client_pk: usePk(client),
                    from_agent_pk: client.curator_pk,
                    to_agent_pk: randomElement(data.to_agent_pks),
                    is_fixed: client.is_fixed,
                }
            }).filter(Boolean)

            return this.response({
                state_updates: updates,
            })
        },

        abortTransfer: async function(data) {
            await deleteModelRecord('AgentClientTransfer', data.transfer_pk)
        },

        unassignAllClients: async function() {
            const clients = await findModelRecords('Client')

            const updates: z.infer<typeof AgentClientTransferState> = clients.map(client => {
                if (!client.curator_pk) {
                    return
                }

                return {
                    client_pk: usePk(client),
                    from_agent_pk: client.curator_pk,
                    to_agent_pk: null,
                    is_fixed: client.is_fixed,
                }
            }).filter(Boolean)

            return this.response({
                state_updates: updates,
            })
        },

        downloadTransferSummary: async function() {
            return this.response({
                url: '#',
            })
        },
    },
})
