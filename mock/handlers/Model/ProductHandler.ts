import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { ProductFieldType } from '~/api/models/Product/ProductAdditionalFieldAmount'
import type { ModelAttributes } from '~types/lib/Model'
import { createModelRecord, deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

const modelName = 'Product'

const composeProductAmounts = (product: ModelAttributes<'Product'>) => {
    const amounts: {
        field_type: ProductFieldType,
        amount: number,
    }[] = []

    Object.keys(ProductFieldType).forEach(key => {
        const field = ProductFieldType[key as keyof typeof ProductFieldType]

        if (product[field] !== undefined) {
            amounts.push({
                field_type: field,
                amount: product[field],
            })
        }
    })

    return amounts
}

export default defineModelHandler<typeof modelName>({
    actions: {
        //
    },

    searchFields: {
        keywords: (record) => `${record.id} ${record.external_number} ${record.order_number} ${record.remark}`.toLowerCase(),
        is_active: (record) => 1, // TODO: airline report used
    },

    observers: {
        afterCreate: async (record) => {
            await createModelRecord('ProductAdditionalFieldAmount', {
                id: record.id,
                amounts: composeProductAmounts(record),
            })
        },
        afterUpdate: async (record) => {
            await updateModelRecord('ProductAdditionalFieldAmount', usePk(record), {
                id: record.id,
                amounts: composeProductAmounts(record),
            })
        },
        afterDelete: async (record) => {
            await deleteModelRecord('ProductAdditionalFieldAmount', usePk(record))
        },
    },
})
