import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { usePk } from '~/composables/usePk'
import {
    deleteModelRecords,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import IssueSeeder from '~mock/seeds/models/Issue/IssueSeeder'
import { IssueCategory } from '~/api/models/Issue/Issue'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { ClientCabinetTransactionType, ClientInfoLogAction } from '~/api/models/Client/Client'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import type { ModelAttributes } from '~types/lib/Model'

const modelName = 'Client'

export default defineModelHandler<typeof modelName>({
    actions: {
        claimSale: async function(data) {
            await (new IssueSeeder()).create({
                model_name: 'Sale',
                model_pk: data.sale_pk,
                category: IssueCategory.SplitSale,
            })
        },

        setDefaultPhone: async function({ client_pk, phone_pk }) {
            await updateModelRecord('Client', client_pk, { client_phone_pk: phone_pk })
        },

        setDefaultEmail: async function({ client_pk, email_pk }) {
            await updateModelRecord('Client', client_pk, { client_email_pk: email_pk })
        },
        tryExactIdentifyClient: async function({ phone }) {
            const client = (await findModelRecords('Client'))[0]

            return this.response({
                pk: client._pk,
                first_name: client.first_name,
                last_name: client.last_name,
                email: client.email,
                phone: phone,
                curator_pk: client.curator_pk,
                is_fixed: client.is_fixed,
                is_preview: false,
            })
        },
        tryIdentifyClient: async function({ phone }) {
            const clients = (await findModelRecords('Client')).splice(0, 10)
                .map((client) => {
                    return {
                        pk: client._pk,
                        first_name: client.first_name,
                        last_name: client.last_name,
                        email: client.email,
                        phone: phone,
                        curator_pk: client.curator_pk,
                        is_fixed: client.is_fixed,
                        is_preview: randomBoolean(),
                    }
                })

            return this.response(clients)
        },
        registryClientCall: async function({
                                                client_pk,
                                                call_id,
                                                to_ringcentral_id,
                                                from_ringcentral_id,
                                                from_phone_number,
                                                payload,
                                            }) {

        },
        findClientsBySearchString: async function({ search_string }) {
            const clients = (await findModelRecords('Client')).splice(0, 10)
                .map((client) => {
                    return {
                        pk: client._pk,
                        first_name: client.first_name,
                        last_name: client.last_name,
                        email: client.email,
                        phone: search_string,
                        curator_pk: client.curator_pk,
                        is_fixed: client.is_fixed,
                        is_preview: randomBoolean(),
                    }
                })

            return this.response(clients)
        },
        block: async function({ client_pk, reason }) {
            await updateModelRecord('Client', client_pk, {
                blocked_reason: reason,
                blocked_at: dateToUnixTimestamp(new Date()),
                blocked_by_pk: useMockAuth().pk,
            })
        },

        updateClientName: async function({ client_pk, first_name, last_name }) {
            await updateModelRecord('Client', client_pk, {
                first_name: first_name,
                last_name: last_name,
            })
        },

        changeCuratorFixedStatus: async function({ client_pk, is_fixed }) {
            await updateModelRecord('Client', client_pk, {
                is_fixed: is_fixed,
            })
        },

        setCurator: async function({ client_pk, agent_pk }) {
            await updateModelRecord('Client', client_pk, {
                curator_pk: agent_pk,
            })
        },

        getActiveCurator: async function(data) {
            // To mock the case when the client has a curator
            if (data.is_unknown_phone) {
                return this.response({
                    curator_pk: randomElementPk(await findModelRecords('Agent')),
                })
            }

            return this.response({ curator_pk: null })
        },

        getCabinetInfo: async function({ pk }) {
            const names = {
                first_name: faker.person.firstName(),
                last_name: faker.person.lastName(),
            }
            const data = {
                person: names,
                contact: {
                    email: faker.internet.email({ firstName: names.first_name, lastName: names.last_name }),
                    phone: faker.phone.number(),
                },
                referral: {
                    hold: randomInt(0, 500),
                    active: randomInt(0, 500),
                },
                deleted_at: dateToUnixTimestamp(faker.date.recent()),
            }

            return this.response(data)
        },

        getCabinetTransactions: async function() {
            const sales = await findModelRecords('Sale')
            const transactions = []
            for (let i = 1; i < randomInt(10, 20); i++) {
                const type = randomEnumValue(ClientCabinetTransactionType)
                transactions.push({
                    id: i + 1,
                    created_at: dateToUnixTimestamp(faker.date.recent({ days: randomInt(1, 10) })),
                    amount: randomFloat(-100, 100),
                    type,
                    payload: {
                        description: randomBoolean() ? faker.lorem.words(3) : null,
                        sale_pk: randomBoolean() ? randomElementPk(sales) : null,
                        pnrs: randomBoolean() ? [faker.lorem.words(1)] : null,
                    },
                })
            }

            return this.response({
                transactions,
            })
        },

        getCabinetLogs: async function() {
            const logs = []
            for (let i = 0; i < randomInt(10, 20); i++) {
                logs.push({
                    id: i + 1,
                    created_at: dateToUnixTimestamp(faker.date.recent({ days: randomInt(1, 10) })),
                    model_id: randomInt(1, 10),
                    model_name: faker.lorem.words(1),
                    action: randomEnumValue(ClientInfoLogAction),
                    message: faker.lorem.words(3),
                    agent_pk: randomBoolean() ? String(randomInt(1, 10)) : null,
                })
            }

            return this.response({
                logs,
            })
        },

        moveClientLink: async function({ pk, client_cabinet_id }) {
            await updateModelRecord('Client', pk, {
                client_cabinet_id,
            })
        },

        createClientLink: async function({ pk, email }) {
            //
        },

        createCabinetTransaction: async function({}) {

        },

        restoreAccount: async function({ pk }) {

        },

        agentHasSeenEnriched: async function({ client_pk }) {

        },
    },

    searchFields: {
        keywords: (record: ModelAttributes<'Client'>) => `${record.id} ${record.first_name} ${record.last_name}`.toLowerCase(),
        has_online_cabinet: (record: ModelAttributes<'Client'>) => {
            return record.client_cabinet_id ? 1 : 0
        },
        has_enrichment_info: async (record: ModelAttributes<'Client'>) => {
            return randomBoolean() ? 1 : 0
        },
    },

    observers: {
        afterDelete: async (record) => {
            await deleteModelRecords('Lead', {
                client_pk: usePk(record),
            })
        },
    },
})
