import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import AgentTargetFactory from '~mock/factories/Agent/AgentTargetFactory'

export default defineModelHandler<'AgentTarget'>({
    actions: {
        updateTarget: async function(data) {
            const target = await findModelRecord('AgentTarget', {
                agent_pk: data.agent_pk,
                month: data.month,
                year: data.year,
            })

            if (target) {
                await updateModelRecord('AgentTarget', target._pk, { value: data.value })
            } else {
                await new AgentTargetFactory().create(data)
            }
        },
    },
})
