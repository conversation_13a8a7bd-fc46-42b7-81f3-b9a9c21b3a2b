import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElement, randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import { usePk } from '~/composables/usePk'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'
import type { ModelAttributes } from '~types/lib/Model'
import { getFullName } from '~/lib/Helper/PersonHelper'

const modelName = 'Voucher'

export default defineModelHandler<typeof modelName>({
    actions: {
        getVoucherSummaryInfo: async function({ status, created_at }) {
            const vouchers = await findModelRecords('Voucher')

            const filtered = vouchers.filter(voucher => {
                if (!!status) {
                    return status.includes(voucher.status)
                }
            })

            const result = {
                inhouse_net: 0,
                airline_net: 0,
                total: 0,
            }
            for (const voucher of filtered) {
                const type = randomElement(['inhouse_net', 'airline_net'])
                result[type] += voucher.net_price
            }

            result.total = result.airline_net + result.inhouse_net

            return this.response(result)
        },

        createVoucher: async function() {
            const voucher = randomElement(await findModelRecords('Voucher'))

            return this.response({
                pk: usePk(voucher),
                voucher_id: voucher.voucher_id,
            })
        },
        editVoucher: async function({ pk, data }) {
            await updateModelRecord(modelName, pk, data)
        },

        deleteVoucher: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },

        sendToClient: async function({ pk }) {
            //
        },

        useVoucher: async function({ voucher_pk, voucher_value, used_voucher_value, sale_reflected_pk }) {
            //
        },

        findClientVoucher: async function({ voucher_id }) {
            const vouchers = await findModelRecords('Voucher')
            const voucher = randomElement(vouchers)

            return this.response({
                voucher_pk: usePk(voucher),
            })
        },

        getVoucherInfo: async function({ sale_pk }) {
            const airlines = await findModelRecords('Airline')
            const clients = await findModelRecords('Client')
            const sales = await findModelRecords('Sale')
            const pnrs = ['ASF##$1', 'ASF##$2', 'ASF##$3']
            const selected = randomElements(clients, randomInt(3, 6))
            const passengers_info = selected.map(client => {
                return {
                    pk: usePk(client),
                    related_pnr: randomElement(pnrs),
                    first_name: client.first_name,
                    last_name: client?.last_name || '',
                    middle_name: '',
                    is_available: randomBoolean(),
                    airline_pk: randomElementPk(airlines),
                }
            })

            const data = {
                voucher_id: 'TBC1234',
                passengers_info: passengers_info,
                available_airline_pks: randomElements(airlines, randomInt(1, 3)).map(airline => usePk(airline)),
                sale_info: {
                    net_amount: randomInt(1, 10000),
                    emd_amount: randomInt(1, 10000),
                    points_trade_amount: randomInt(1, 10000),
                    upgrade_amount: randomInt(1, 10000),
                    fop: 'FOP value',
                    pcc: ['AAA', 'BBB', 'CCC'],
                    executor_pk: randomElement(sales).executor_pk,
                },
            }

            return this.response(data)
        },

        getVoucherClientInfo: async function({ client_pk }) {
            const sale_version_cards = await findModelRecords('SaleVersionCard')
            const client_cards = randomElements(sale_version_cards, randomInt(1, 4))

            return this.response({
                voucher_id: 'TBC1234',
                cards: client_cards.map((card: ModelAttributes<'SaleVersionCard'>) => {
                    return {
                        pk: usePk(card),
                        strip: card.strip,
                        card_holder_name: getFullName(card),
                        first_address: `${card.state} ${card.country} ${card.city} ${card.street}`,
                        second_address: `${card.postal_code}`,
                    }
                }),
            })
        },

        createClientVoucher: async function({ client_pk, data }) {
            //
        },

        editClientVoucher: async function({ pk, data }) {
            //
        },

        linkWithExpectedAmount: async function({ pk, expected_amount_pk }) {
            await updateModelRecord('Voucher', pk, {
                expected_amount_pk,
            })
        },
    },

})
