import { define<PERSON>odelHand<PERSON> } from '~mock/utils/define'
import SaleTransactionFactory from '~mock/factories/Sale/SaleTransactionFactory'

const modelName = 'SaleTransaction'
const saleTransactionFactory = new SaleTransactionFactory()

export default defineModelHandler<typeof modelName>({
    actions: {
        addTransaction: async function(data) {
            await saleTransactionFactory.create({
                sale_pk: data.sale_pk,
                sale_version_card_pk: data.sale_version_card_pk,
                client_card_pk: data.sale_version_card_pk,
                amount: data.amount,
            })
        },

        repeat: async function(data) {},
        refund: async function(data) {},
        cancel: async function(data) {},
        capture: async function(data) {},
    },

})
