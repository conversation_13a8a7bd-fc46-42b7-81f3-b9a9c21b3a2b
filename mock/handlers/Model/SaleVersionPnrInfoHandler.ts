import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
createModelRecord,
findModelRecord,
findModelRecordOrFail,
nextIncrementValue,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'SaleVersionPnrInfo'

export default defineModelHandler<typeof modelName>({
    actions: {
        updatePnrInfo: async function(data: {
            pnr_pk: PrimaryKey, type: PnrInfoType, info: {
                eligibility: boolean | undefined,
                penalty: number | undefined,
                non_refundable_tax: boolean | undefined,
                validity: number | undefined,
                commission: number | undefined,
                refund_amount: number | undefined,
                fop: string | undefined,
            }
        }) {
            const pnrInfoRecord = await findModelRecord('SaleVersionPnrInfo',   { pnr_pk: data.pnr_pk, type: data.type })

            if (pnrInfoRecord) {
                await updateModelRecord('SaleVersionPnrInfo', usePk(pnrInfoRecord), {
                    eligibility: data.info.eligibility,
                    penalty: data.info.penalty,
                    non_refundable_tax: data.info.non_refundable_tax,
                    validity: data.info.validity,
                    commission: data.info.commission,
                    refund_amount: data.info.refund_amount,
                    fop: data.info.fop,
                })
            } else {
                await createModelRecord('SaleVersionPnrInfo', {
                    id: nextIncrementValue(modelName),
                    pnr_pk: data.pnr_pk,
                    type: data.type,
                    eligibility: data.info.eligibility,
                    penalty: data.info.penalty,
                    non_refundable_tax: data.info.non_refundable_tax,
                    validity: data.info.validity,
                    commission: data.info.commission,
                    refund_amount: data.info.refund_amount,
                    fop: data.info.fop,
                })
            }
        },
    },
})
