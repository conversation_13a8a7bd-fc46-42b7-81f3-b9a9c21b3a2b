import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import CompanyContactFactory from '~mock/factories/CompanyContact/CompanyContactFactory'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import AirlineCompanyContactFactory from '~mock/factories/CompanyContact/AirlineCompanyContactFactory'
import { DepartmentName } from '~/api/models/Department/Department'
import type { ModelAttributes } from '~types/lib/Model'
import { usePk } from '~/composables/usePk'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { CompanyContactStatus } from '~/api/models/CompanyContact/CompanyContact'

export default defineModelHandler<'CompanyContact'>({
    actions: {
        createContact: async function({ data }) {
            await (new CompanyContactFactory()).create(data)
        },

        deleteContact: async function({ pk }) {
            await updateModelRecord('CompanyContact', pk, {
                is_deleted: true,
            })
        },

        blockContact: async function({ pk }) {
            const contact = await findModelRecord('CompanyContact', pk)
            const status = contact?.status === CompanyContactStatus.Active ? CompanyContactStatus.Locked : CompanyContactStatus.Active
            await updateModelRecord('CompanyContact', pk, {
                status,
            })
        },

        saveContact: async function({ pk, data }) {
            if (!pk) {
                await createModelRecord('CompanyContact', data)
            } else {
                await updateModelRecord('CompanyContact', pk, data)
            }
        },

        csSwitchCaseContact: async function({ airline_pk, old_contact_pk, contact_pk }) {
            const assignment = await findModelRecord('AirlineCompanyContact', {
                airline_pk,
                contact_pk: old_contact_pk,
            })

            if (!assignment) {
                return
            }

            await updateModelRecord('AirlineCompanyContact', usePk(assignment), {
                contact_pk,
            })
        },

        getUsedInSaleContacts: async function({ sale_pk }) {
            const result = []

            const assignments = await findModelRecords('AirlineCompanyContact', {
                sale_pk,
            })

            for (const assignment of assignments) {
                const contact = await findModelRecord('CompanyContact', assignment.contact_pk) as ModelAttributes<'CompanyContact'>
                const airline = await findModelRecord('Airline', assignment.airline_pk) as ModelAttributes<'Airline'>
                result.push({
                    contact_pk: assignment.contact_pk,
                    value: contact.value,
                    airline_pk: assignment.airline_pk,
                    category: contact.category,
                    airline: {
                        code: airline.code,
                    },
                })
            }

            return this.response(result)
        },

        getAvailableContactsForCs: async function({ category, sub_category, airline_pk }) {
            const result = []

            const csDepartment = findModelRecord('Department', {
                system_name: DepartmentName.CustomerSupport,
            })
            const contacts = await findModelRecords('CompanyContact', { category, department_pk: usePk(csDepartment) })

            for (const contact of contacts) {
                const assignments = await findModelRecords('AirlineCompanyContact', {
                    contact_pk: usePk(contact),
                })

                const assignedAirlinePks = assignments.map(assignment => assignment.airline_pk)

                if (!assignedAirlinePks.includes(airline_pk) && (!sub_category || contact.sub_category === sub_category)) {
                    result.push(contact)
                }
            }

            return this.response(result)
        },

        getAvailableContactsForTicketing: async function({ category, sale_pk, airline_pks }) {
            if (airline_pks?.length) {
                const revTktDepartment = await findModelRecord('Department', {
                    system_name: DepartmentName.TicketingRevenue,
                })
                const contacts = await findModelRecords('CompanyContact', {
                    category,
                    department_pk: usePk(revTktDepartment),
                })

                return this.response(contacts)
            }

            return this.response([])
        },

        getAvailableContactsForAward: async function({ mile_price_program_pk }) {
            const awardTktDepartment = await findModelRecord('Department', {
                system_name: DepartmentName.TicketingAward,
            })

            const contacts = await findModelRecords('CompanyContact', {
                department_pk: usePk(awardTktDepartment),
                is_deleted: false,
                status: CompanyContactStatus.Active,
            })

            return this.response(contacts)
        },

        getFakePhone: async function({ sale_pk }) {
            const sale = await findModelRecord('Sale', sale_pk)

            if (sale?.client_pk) {
                const client = await findModelRecord('Client', sale.client_pk)

                return this.response(client?.phone ?? 'No client phone')
            }

            return this.response('No client phone')
        },

        checkAirlineContact: async function({ pk, sale_pk, airline_pk, case_number }) {
            await new AirlineCompanyContactFactory().create({
                contact_pk: pk,
                sale_pk,
                airline_pk,
                case_number,
            }, { departmentName: case_number ? DepartmentName.CustomerSupport : DepartmentName.TicketingRevenue })
        },

        checkAirlineContactForSale: async function({ pk, sale_pk, airline_pks }) {
            for (const airline_pk of airline_pks) {
                const contact = await findModelRecord('AirlineCompanyContact', {
                    contact_pk: pk,
                    sale_pk,
                    airline_pk,
                })

                if (!contact) {
                    await new AirlineCompanyContactFactory().create({
                        contact_pk: pk,
                        sale_pk,
                        airline_pk,
                    }, { departmentName: DepartmentName.TicketingRevenue })
                }
            }
        },

        uncheckAirlineContact: async function({ pk, sale_pk, airline_pk }) {
            const found = await findModelRecord('AirlineCompanyContact', {
                contact_pk: pk,
                sale_pk,
                airline_pk,
            })

            if (found) {
                await deleteModelRecord('AirlineCompanyContact', usePk(found))
            }
        },

        getAirlineUsageCount: async function({ department_pk, category, sub_category }) {
            const airlines = await findModelRecords('Airline')
            const airlines_for_response = randomElements(airlines, 20)
            const result = []

            for (const airline of airlines_for_response) {
                result.push({
                    pk: String(airline.id),
                    count: randomInt(0, 20),
                })
            }

            return this.response(result)
        },

        getMilePriceProgramUsageCount: async function({ department_pk, category }) {
            const milePricePrograms = await findModelRecords('MilePriceProgram')

            const result = []

            for (const milePriceProgram of milePricePrograms) {
                result.push({
                    pk: String(milePriceProgram.id),
                    count: randomInt(0, 20),
                })
            }

            return this.response(result)
        },

        checkMilePriceProgramContact: async function({ pk, mile_price_program_pk }) {
            await new AirlineCompanyContactFactory().create({
                contact_pk: pk,
                mile_price_program_pk,
            }, { departmentName: DepartmentName.TicketingAward })
        },

        uncheckMilePriceProgramContact: async function({ pk, mile_price_program_pk }) {
            const found = await findModelRecord('AirlineCompanyContact', {
                contact_pk: pk,
                mile_price_program_pk,
            })

            if (found) {
                await deleteModelRecord('AirlineCompanyContact', usePk(found))
            }
        },
    },

    searchFields: {
        keywords(record: ModelAttributes<'CompanyContact'>) {
            return String(record.value)
        },
    },

})
