import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

const modelName = 'TaskGroup'

export default defineModelHandler<typeof modelName>({
    actions: {
        // No actions
    },

    observers: {
        afterDelete: async function(taskGroup) {
            await deleteModelRecords('Task', {
                task_group_pk: usePk(taskGroup),
            })
        },
    },
})
