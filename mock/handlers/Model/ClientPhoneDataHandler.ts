import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecordOrFail,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import {  usePk } from '~/composables/usePk'

export default defineModelHandler <'ClientPhoneData'>({
        actions: {
            create: async ({ client_pk, phone }) => {
                const client = await findModelRecordOrFail('Client', client_pk)
                const phoneModel = await createModelRecord('Phone', { value: phone })
                await createModelRecord('ClientPhoneData', { client_pk: usePk(client), phone_pk: usePk(phoneModel) })
            },
            update: async ({ pk, phone }) => {
                const clientPhoneData = await findModelRecordOrFail('ClientPhoneData', pk)
                await updateModelRecord('Phone', clientPhoneData.phone_pk, { value: phone })
            },
            remove: async ({ pk }) => {
                await deleteModelRecord('ClientPhoneData', pk)
            },
            saveRemark: async ({ pk, remark }) => {
                await updateModelRecord('ClientPhoneData', pk, { remark })
            },
        },
        searchFields: {},
        observers: {},
    },
)
