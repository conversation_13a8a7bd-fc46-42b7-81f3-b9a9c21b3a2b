import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    deleteModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'
import AwardAccountOutgoingRequestFactory from '~mock/factories/AwardAccount/AwardAccountOutgoingRequestFactory'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { recalculateAccountBalance } from '~mock/handlers/Model/AwardAccountHandler'

const modelName = 'AwardAccountOutgoingRequest'

export default defineModelHandler<typeof modelName>({
    actions: {
        editRemark: async function({ pk, remark }) {
            await updateModelRecord('AwardAccountOutgoingRequest', pk, { remark })
        },

        delete: async function({ pk }) {
            await deleteModelRecord('AwardAccountOutgoingRequest', pk)
        },
    },
    observers: {
        afterCreate: async function(request) {
            if (request.parent_pk) {
                return
            }

            const amount = request.miles_amount
            const incoming = await findModelRecords('AwardAccountIncomingRequest', { award_account_pk: request.award_account_pk })

            const availableForExtraction = incoming.filter(incomingRequest => incomingRequest.balance > 0)

            if (!availableForExtraction.length) {
                return
            }

            const availableForExtractionRequest = availableForExtraction[0]

            if (amount <= 0) {
                return
            }

            const diff = availableForExtractionRequest.balance - amount

            if (diff >= 0) {
                await updateModelRecord('AwardAccountIncomingRequest', usePk(availableForExtractionRequest), { balance: diff })
                await updateModelRecord('AwardAccountOutgoingRequest', usePk(request), {
                    parent_pk: usePk(availableForExtractionRequest),
                    price: availableForExtractionRequest.price,
                })
            } else {
                await new AwardAccountOutgoingRequestFactory().create({
                    miles_amount: amount - availableForExtractionRequest.balance,
                    award_account_pk: request.award_account_pk,
                    parent_pk: null,
                    product_pk: request.product_pk,
                    remark: request.remark,
                    price: availableForExtractionRequest.price, //
                    created_at: dateToUnixTimestamp(new Date()),
                })
            }
        },

        afterDelete: async function(request) {
            if (!request?.parent_pk || !request.product_pk) {
                return
            }

            const incoming = await findModelRecordOrFail('AwardAccountIncomingRequest', request.parent_pk)

            if (incoming) {
                await updateModelRecord('AwardAccountIncomingRequest', usePk(incoming), { balance: incoming.balance + request.miles_amount })

                const ticket = await findModelRecordOrFail('Ticket', { product_pk: request.product_pk })

                await updateModelRecord('Ticket', usePk(ticket), { award_account_pk: null })
            }

            await recalculateAccountBalance(incoming.award_account_pk)
        },
    },
})
