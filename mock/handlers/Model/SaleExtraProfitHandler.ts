import { define<PERSON>odel<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'SaleExtraProfit'

export default defineModelHandler<typeof modelName>({
    actions: {
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },
        save: async function({ pk, data }) {
            await updateModelRecord(modelName, pk, data)
        },
    },

    searchFields: {
        sale_pk: (record) => record.sale_pk,
    },

})
