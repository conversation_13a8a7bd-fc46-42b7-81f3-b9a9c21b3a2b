import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

export default defineModelHandler<'AgentReportInternalProfit'>({
    actions: {
        save: async function({ invoice_pk, data }) {
            for (const item of data) {
                await updateModelRecord('AgentReportInternalProfit', item.pk, { remark: item.remark })
            }
        },
    },
})
