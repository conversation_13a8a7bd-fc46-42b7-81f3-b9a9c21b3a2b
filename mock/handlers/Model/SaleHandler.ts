import { faker } from '@faker-js/faker'
import { usePk } from '~/composables/usePk'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import {
    deleteModelRecord,
    deleteModelRecords,
    findModelRecord,
    findModelRecordOrFail,
    findModelRecords,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { ofetch } from 'ofetch'
import { createHash } from 'crypto'
import { throwError } from '~mock/lib/Helper/ErrorHelper'
import { nullPropsToUndefined } from '~/lib/Helper/ObjectHelper'
import { defineModelHandler } from '~mock/utils/define'

const modelName = 'Sale'

export default defineModelHandler<typeof modelName>({
    actions: {
        createRefundFromClient: async function() {
            const sale = await findModelRecordOrFail('Sale', '1')

            return this.response({
                sale_pk: usePk(sale),
            })
        },

        clone: async function() {
            const sale = await findModelRecordOrFail('Sale', '1')

            return this.response({
                sale_pk: usePk(sale),
            })
        },

        addDisclaimer: async function() {
            // do nothing
        },

        shareSale: async function() {
            //do nothing
        },
        updateShareSale: async function() {
            //do nothing
        },
        shareExpertGp: async function() {
            // do nothing
        },

        fetchSaleActivityMembers: async function({ sale_pk }) {
            const agents = await findModelRecords('Agent')

            return this.response({
                sale_activity_members_pks: agents.splice(0, 12).map((agent) => usePk(agent)),
            })
        },

        fetchSaleSummaryMembers: async function({ from_date, to_date }) {
            const agents = (await findModelRecords('Agent')).splice(0, 40).map((agent) => {
                return {
                    agent_pk: usePk(agent),
                    percent: faker.number.float({ max: 100, min: 20 }),
                }
            })

            return this.response({
                sale_summary_members: agents,
            })
        },

        fetchSaleExpertMembers: async function() {
            return {}
        },

        updateSaleWithType: async function() {
            // do nothing
        },
        createNewSaleWithType: async function() {
            const sale_pk = randomElementPk(await findModelRecords('Sale'))

            return this.response({
                sale_pk,
            })
        },

        createNewSaleForClient: async function() {
            const sale_pk = randomElementPk(await findModelRecords('Sale'))

            return this.response({
                sale_pk,
            })
        },

        fetchSaleListExcel: async function({ search_params }) {
            const b = Buffer.from(JSON.stringify(search_params))
            const base64 = b.toString('base64')

            return this.response({
                result: base64,
            })
        },

        mixerMarkupRequest: async function(data, _, event) {
            const config = useRuntimeConfig(event)

            const fetch = ofetch.create({
                baseURL: config.markup.apiBase,
            })

            const timestamp = Math.floor(Date.now() / 1000)

            const secret = config.markup.secret
            const token = createHash('sha256').update(`${timestamp}_${secret}`).digest('hex')

            try {
                const response = await fetch<any>(data.url, {
                    ...data.options,
                    params: {
                        ...data.options.params,
                        token,
                        timestamp,
                    },
                    body: nullPropsToUndefined(data.options.body),
                })

                return this.response(response)
            } catch (e: any) {
                throw throwError(event, 'Something went wrong', {
                    status: e?.status || 500,
                    data: e?.data || {},
                })
            }
        },

        sendPriceDropStagingTest: async function({ pk }) {
            //
        },
    },

    searchFields: {
        client_name: async (record) => {
            const client = await findModelRecord('Client', record.client_pk)

            if (client) {
                return `${client.first_name} ${client.last_name}`.toLowerCase()
            }

            return ''
        },
        passenger_name: async (record) => {
            const saleVersion = await findModelRecord('SaleVersion', { sale_pk: usePk(record), is_active: true })

            if (!saleVersion) {
                return ''
            }
            const passengers = await findModelRecords('SaleVersionPassenger', { sale_version_pk: usePk(saleVersion) })

            if (!passengers.length) {
                return ''
            }
            let keywords = ''
            passengers.forEach(passenger => {
                keywords += ` ${passenger.first_name} ${passenger.last_name} `
            })

            return keywords
        },
        from_iata_code: async (record) => {
            const saleVersion = await findModelRecord('SaleVersion', { sale_pk: usePk(record), is_active: true })

            if (!saleVersion) {
                return ''
            }

            return saleVersion.from_iata_code
        },
        to_iata_code: async (record) => {
            const saleVersion = await findModelRecord('SaleVersion', { sale_pk: usePk(record), is_active: true })

            if (!saleVersion) {
                return ''
            }

            return saleVersion.to_iata_code
        },
        departure_at: async (record) => {
            const saleVersion = await findModelRecord('SaleVersion', { sale_pk: usePk(record), is_active: true })

            if (!saleVersion) {
                return undefined
            }

            return saleVersion.departure_at
        },

        can_be_added_to_agent_invoice_pk: async (record) => {
            // const invoice = await findModelRecord('AgentReportInvoice', { sale_pk: usePk(record) })

            return true
        },

        is_pending_approval: async (record) => {
            return record.is_pending_approval ? 1 : 0
        },

        has_upgrade: async (record) => {
            return true
        },

        has_segment_type: async () => {
            return randomElement(['extra_leg', 'fake_return'])
        },
    },

    observers: {
        async afterDelete(record) {
            const pk = usePk(record)

            await deleteModelRecord('SaleAdditionalProfits', pk)

            await deleteModelRecords('Chat', {
                model_name: modelName,
                model_pk: pk,
            })

            await deleteModelRecords('TaskGroup', {
                model_name: modelName,
                model_pk: usePk(record),
            })
        },
    },
})
