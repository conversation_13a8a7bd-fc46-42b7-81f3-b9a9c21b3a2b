import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecord,
    findModelRecordOrFail,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

const modelName = 'ClientPassport'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ data }) {
            await createModelRecord(modelName, { ...data, is_default: false })
        },
        update: async function({ pk, data }) {
            await updateModelRecord(modelName, pk, data)
        },
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },
        convertToClient: async function({ pk }) {
            await updateModelRecord(modelName, pk, {
                is_belongs_to_client: true,
            })
        },
        setDefault: async function({ pk }) {
            const activePassport = await findModelRecordOrFail(modelName, pk)

            const oldDefaultRecord = await findModelRecord(modelName, {
                client_pk: activePassport.client_pk,
                is_default: true,
            })

            if (oldDefaultRecord) {
                await updateModelRecord(modelName, usePk(oldDefaultRecord), {
                    is_default: false,
                })
            }

            await updateModelRecord(modelName, pk, {
                is_default: true,
            })
        },

    },
})
