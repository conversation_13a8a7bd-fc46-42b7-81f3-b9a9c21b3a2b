import { findModelRecordOrFail, findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { pluckPks, randomElement } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { defineModel<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
export default defineModelHandler<'SignedDocument'>({
    actions: {
        getSettlementDocumentData: async function({ sale_version_pk }) {
            const saleVersion = await findModelRecordOrFail('SaleVersion', sale_version_pk)

            const pnrs = await findModelRecords('SaleVersionPnr', { sale_version_pk })

            const airlines = (await findModelRecords('Airline')).slice(0, 10)

            return this.response({
                pnrs: pluckPks(pnrs),
                ticket_count: faker.number.int({ min: 1, max: 4 }),
                airlines: airlines.map((airline) => airline.name),
                from: saleVersion.from_iata_code,
                to: saleVersion.to_iata_code,
                purchase_at: Date.now(),
                ticket_costs: faker.number.float(),
                refund_amount: faker.number.float(),
                travel_credit_amount: faker.number.float(),
                travel_credit_expire_at: Date.now(),
                travel_credit_airlines: airlines.map((airline) => airline.name),
            })
        },
        generatePreviewDocument: async function({ html_data }) {
            const files = await findModelRecords('File')

            return this.response({
                url: 'http://gmail.com',
                file_pk: usePk(randomElement(files)),
            })
        },

        sendDocument: async function({ sale_version_pk, file_pk, email, category }) {
            await createModelRecord('SignedDocument', {
                type: category,
                sent_at: Date.now(),
                viewed_at: Date.now(),
                signed_at: Date.now(),
            })
        },
    },
})
