import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import {
createModelRecord,
deleteModelRecord,
updateModelRecord,
findModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { CreditCardType, SaleCardVerifyStatus } from '~/api/models/Sale/SaleVersionCard'
import { faker } from '@faker-js/faker'
const modelName = 'SaleVersionCard'
export default defineModelHandler<typeof modelName>({
    actions: {
        requestTestAmount: async function({ pk, sale_pk }) {
            const amount = randomFloat(10, 50)

            return this.response({
                amount,
            })
        },

        create: async function(saleCardData) {
            const data = saleCardData.data

            const saleVersion = await findModelRecord('SaleVersion', saleCardData.sale_version_pk)

            const card = await createModelRecord('SaleVersionCard', {
                sale_version_pk: saleCardData.sale_version_pk,
                sale_pk: saleVersion?.sale_pk ?? '',
                amount: data.amount,
                bank_phone: data.bank_phone,
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
                postal_code: data.zip,
                expiration: data.expiration,
                street: data.street,
                city: data.city,
                state: data.state,
                country: data.country,
                is_verified: true,
                credit_card_type: randomEnumValue(CreditCardType),
                strip: data.card.slice(0, 4) + '-' + data.card.slice(-4),
                verify_status: randomEnumValue(SaleCardVerifyStatus),
                is_wrong: false,
            })

            return this.response({
                card_pk: usePk(card),
            })
        },

        update: async function(saleCardData) {
            const data = saleCardData.data
            await updateModelRecord('SaleVersionCard', saleCardData.card_pk, {
                amount: data.amount,
                strip: data.card.slice(0, 4) + '-' + data.card.slice(-4),
                bank_phone: data.bank_phone,
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
                postal_code: data.zip,
                street: data.street,
                city: data.city,
                state: data.state,
                country: data.country,
            })
        },

        delete: async function(data) {
            await deleteModelRecord('SaleVersionCard', data.pk)
        },

        updateOafSplit: async function(data) {
            Object.entries(data.data).forEach(async ([key, value]) => {
                await updateModelRecord('SaleVersionCard', key, { amount: value })
            })
        },

        changeIsWrong: async function(data) {
            await updateModelRecord('SaleVersionCard', data.pk, {
                is_wrong: data.is_wrong,
            })
        },

        verifyCard: async function(data) {
            const status = data.test_amount ? SaleCardVerifyStatus.Complete : SaleCardVerifyStatus.Waiting
            await updateModelRecord('SaleVersionCard', data.pk, {
                is_verified: data.test_amount,
                verify_status: status,
            })

            return this.response({
                success: true,
                try_count: 0,
            })
        },

        getUsedBeforeSalePks: async function(data) {
            return this.response({
                sale_pks: ['15', '16', '19'],
            })
        },

        requestCardSensitiveData: async function({ pk }) {
            return this.response({
               cvv: String(randomInt(100, 999)),
               card: faker.finance.creditCardNumber(),
            })
        },

        changeIsVerified: async function(data) {
            await updateModelRecord('SaleVersionCard', data.pk, {
                is_verified: true,
            })
        },
    },
})
