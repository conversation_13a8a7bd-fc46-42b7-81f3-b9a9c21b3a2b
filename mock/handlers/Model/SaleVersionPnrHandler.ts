import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'
import { findModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'SaleVersionPnr'

export default defineModelHandler<typeof modelName>({
    actions: {
        markAsProcessed: async function(pk: PrimaryKey) {
        },
        updatePnrFareType: async function(data) {
            const pk = usePk(await findModelRecord('SaleVersionPnr', data.pnr_pk))
            await updateModelRecord(modelName, pk, {
                fare_type: data.fare_type,
            })
        },
        startWork: async function(data) {

        },
    },
})
