import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    findModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'
import { randomElement, randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { faker } from '@faker-js/faker'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { ProductType } from '~/api/models/Product/Product'
import { PassengerAge } from '~/api/models/Sale/SaleVersionPassenger'

const modelName = 'SaleVersion'

export default defineModelHandler<typeof modelName>({
    actions: {
        getAvailableAirlines: async function() {
            const airlines = await findModelRecords('Airline')

            return this.response(airlines.slice(0, 20).map((airline) => usePk(airline)))
        },
        setDefaultPhone: async function({ sale_version_pk, phone_pk }) {
            await updateModelRecord('SaleVersion', sale_version_pk, { client_phone_pk: phone_pk })
        },

        setDefaultEmail: async function({ sale_version_pk, email_pk }) {
            await updateModelRecord('SaleVersion', sale_version_pk, { client_email_pk: email_pk })
        },

        populateTicket: async function() {
            //
        },
        checkPnr: async function() {
            const error = randomBoolean()

            return this.response({
                current: {
                    pnr: 'sdfsdfd',
                    validating_carrier_pk: '1',
                    consolidator_area_pk: null,
                    net_price: 1000,
                    tax: 100,
                    fare: 200,
                    check_payment: 13,
                    commission: 30,
                    issued_at: 3245234523,
                    issuing_fee: 13,
                    external_number: randomBoolean() ? 'SDF56789' : null,
                    passenger_first_name: 'TESTER',
                    passenger_last_name: 'TESTEROVICH',
                    passenger_middle_name: 'TESTEROV',
                    passenger_birthday_at: 3417507371,
                    passenger_type: PassengerAge.Adult,
                },

                tickets: error ? [] : [
                    {
                        external_number: 'SDF56799',
                        pnr: 'sdfsdfd',
                        validating_carrier_pk: '2',
                        consolidator_area_pk: '1',
                        net_price: 995.63,
                        tax: 110,
                        fare: 190,
                        check_payment: 12.73,
                        commission: 32,
                        issued_at: 3245234523,
                        issuing_fee: 13,
                        passenger_first_name: 'TESTER',
                        passenger_last_name: 'TESTEROVICH',
                        passenger_middle_name: 'TESTEROV',
                        passenger_birthday_at: 3417507372,
                        passenger_type: PassengerAge.Adult,
                    },
                    {
                        external_number: 'SDF56800',
                        pnr: 'sdfsdfd',
                        validating_carrier_pk: '2',
                        consolidator_area_pk: '1',
                        net_price: 995.63,
                        tax: 310,
                        fare: 290,
                        check_payment: 12.73,
                        commission: 32,
                        issued_at: 3245234524,
                        issuing_fee: 13,
                        passenger_first_name: 'TESTER2',
                        passenger_last_name: 'TESTEROVICH2',
                        passenger_middle_name: 'TESTEROV2',
                        passenger_birthday_at: 3417507371,
                        passenger_type: PassengerAge.Child,
                    },
                ],
                error_message: error ? faker.word.words(5) : null,
            })
        },
        applyCheckPnr: async function() {
            //
        },

        getCashUpgradeInfo: async function({ sale_version_pk }) {
            const passengers = await findModelRecords('SaleVersionPassenger', {
                sale_version_pk,
            })
            const airlines = randomElements(await findModelRecords('Airline'), randomInt(1, 3))
            const result = {
                airlines: [],
            }

            for (const airline of airlines) {
                const pnrCount = randomInt(2, 5)
                const airlineItem = {
                    pk: usePk(airline),
                    name: airline.name,
                    pnrs: [],
                }

                for (let i = 1; i < pnrCount; i++) {
                    const itemPassengers = randomElements(passengers, randomInt(1, passengers.length))
                    const pnrItem = {
                        pnr: faker.string.alphanumeric(6),
                        passengers: itemPassengers.map((passenger) => {
                            return {
                                pk: usePk(passenger),
                                first_name: passenger.first_name,
                                last_name: passenger.last_name,
                                middle_name: passenger.middle_name,
                            }
                        }),
                    }
                    airlineItem.pnrs.push(pnrItem)
                }

                result.airlines.push(airlineItem)
            }

            return this.response(result)
        },
        processCashUpgradeInfo: async function({ airline_pk, passengers_pks }) {
            const result = {
                passengers: [],
            }
            //
            for (const pk of passengers_pks) {
                const passenger = await findModelRecord('SaleVersionPassenger', {
                    pk,
                })
                const data = {
                    passenger: {
                        pk: randomBoolean(0.9) ? pk : null,
                        first_name: passenger?.first_name,
                        last_name: passenger?.last_name,
                        middle_name: passenger?.middle_name,
                    },
                    upgrades: [],
                }

                for (let i = 0; i < randomInt(1, 4); i++) {
                    data.upgrades.push({
                        flight_segment: `${faker.location.city()} to ${faker.location.city()}`,
                        emd_price: randomFloat(10, 1000),
                        emd_number: faker.string.alphanumeric(6),
                        upgrade_type: ProductType.CashUpgrade,
                    })
                }

                result.passengers.push(data)
            }

            return this.response(result)
        },

        createCashUpgrades: async function() {
            //
        },

        checkOnlinePnr: async function() {
            const passengers = await findModelRecords('SaleVersionPassenger')

            const result = {
                tickets: [],
            }
            for (let i = 0; i < randomInt(1, 4); i++) {
                const passenger = randomElement(passengers)
                result.tickets.push({
                    ticket_pk: randomBoolean() ? null : '1',
                    pnr: faker.string.alphanumeric(6),
                    airline_pk: randomElementPk(await findModelRecords('Airline')),
                    first_name: passenger.first_name,
                    last_name: passenger.last_name,
                    tkt_number: faker.string.alphanumeric(6),
                    air_pnr: faker.string.alphanumeric(6),
                    fare: randomFloat(50, 1000),
                    tax: randomFloat(10, 100),
                    fop: '',
                })
            }

            return this.response(result)
        },

        applyOnlinePnr: async function() {
            //
        },
    },
    observers: {
        async afterCreate(record) {
            const saleVersions = await findModelRecords('SaleVersion', {
                sale_pk: record.sale_pk,
            })

            const oldVersions = saleVersions.filter(version => version.is_active)

            for (const version of oldVersions) {
                await updateModelRecord('SaleVersion', usePk(version), { is_active: false })
            }
            await updateModelRecord('SaleVersion', usePk(record), { is_active: true })
        },
    },
})
