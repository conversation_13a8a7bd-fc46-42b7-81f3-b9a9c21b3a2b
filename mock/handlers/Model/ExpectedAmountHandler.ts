import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import ExpectedAmountFactory from '~mock/factories/ExpectedAmount/ExpectedAmountFactory'
import { findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'ExpectedAmount'

export default defineModelHandler<typeof modelName>({
    actions: {
        createExpectedAmount: async function({ data }) {
            await (new ExpectedAmountFactory()).create(data)
        },
        updateExpectedAmount: async function({ expected_amount_pk, data }) {
            await updateModelRecord(modelName, expected_amount_pk, {
                ...data,
            })
        },
    },
    searchFields: {
        airline_case_number: async (record) => {
            const airlineCase = await findModelRecord('AirlineCase', record.airline_case_pk)

            if (airlineCase) {
                return airlineCase.case_number
            }

            return ''
        },
    },
})
