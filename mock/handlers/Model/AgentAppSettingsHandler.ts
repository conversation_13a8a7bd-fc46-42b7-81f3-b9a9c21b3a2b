import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecord,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { composePk } from '~/composables/usePk'

export default defineModelHandler<'AgentAppSettings'>({
    actions: {
        save: async function(data) {
            const pk = composePk(data, useResourceDefinition('AgentAppSettings').pk)

            if (!pk) {
                return
            }

            const record = await findModelRecord('AgentAppSettings', pk)

            if (!record) {
                await createModelRecord('AgentAppSettings', data)

                return
            }

            const value = data.value

            if (value) {
                await updateModelRecord('AgentAppSettings', pk, { value: value })
            } else {
                await deleteModelRecord('AgentAppSettings', pk)
            }
        },

        sharePreset: async function({ presets, agent_pks }) {
            return
        },
    },
})
