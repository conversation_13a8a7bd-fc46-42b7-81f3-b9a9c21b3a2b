import { defineM<PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { createModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { useMockAuth } from '~mock/composables/useMockAuth'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'ReleasePostItemFeedbackReply'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ release_post_item_feedback_pk, message }) {
            await createModelRecord(modelName, {
                release_post_item_feedback_pk,
                message,
                created_at: dateToUnixTimestamp(new Date()),
                agent_pk: useMockAuth().pk,
            })
        },
    },
    searchFields: {
        //
    },

    observers: {
        //
    },
})
