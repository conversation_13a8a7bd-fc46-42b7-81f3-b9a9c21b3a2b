import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecord,
    findModelRecords,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import AgentReportFactory from '~mock/factories/AgentReport/AgentReportFactory'
import { groupBy, randomElementPk } from '~/lib/Helper/ArrayHelper'
import AgentReportInvoiceFactory from '~mock/factories/AgentReport/AgentReportInvoiceFactory'
import { usePk } from '~/composables/usePk'
import { AgentReportInvoiceStatus } from '~/api/models/AgentReport/AgentReportInvoice'
import { AgentReportTicketingType } from '~/api/models/AgentReport/AgentReportTicketing'
import { PositionName } from '~/api/models/Position/Position'

const countTotalElement = (array, key: string): number => {
    return array.map((item): number => {
        return item[key as keyof typeof item] as number
    }).reduce((prev: number, curr: number) => prev + curr, 0)
}

export default defineModelHandler<'AgentReport'>({
    actions: {
        create: async function({ data }) {
            await new AgentReportFactory().create(data)
        },
        sendForApproval: async function({ pk, department_pk }) {
            const invoices = await findModelRecords('AgentReportInvoice', {
                agent_report_pk: pk,
                department_pk: department_pk,
            })

            for (const invoice of invoices) {
                if (invoice.status === AgentReportInvoiceStatus.Processed) {
                    await updateModelRecord('AgentReportInvoice', usePk(invoice), {
                        is_show_for_agent: true,
                        can_approve: true,
                    })
                }
            }
        },
        delete: async function({ pk }) {
            const invoices = await findModelRecords('AgentReportInvoice', {
                agent_report_pk: pk,
            })

            for (const invoice of invoices) {
                const assignments = await findModelRecords('AgentReportSale', {
                    agent_invoice_pk: usePk(invoice),
                })
                for (const assignment of assignments) {
                    await deleteModelRecord('AgentReportSale', usePk(assignment))
                }
                await deleteModelRecord('AgentReportInvoice', usePk(invoice))
            }
            await deleteModelRecord('AgentReport', pk)
        },
        complete: async function({ pk }) {
            await updateModelRecord('AgentReport', pk, {
                completed_at: dateToUnixTimestamp(new Date()),
            })

            const assignments = await findModelRecords('AgentReportSale', { agent_report_pk: pk })
            const groupByAgent = groupBy(assignments, 'agent_pk')

            for (const [agent_pk, assignments] of Object.entries(groupByAgent)) {
                const total = {
                    new: countTotalElement(assignments, 'new'),
                    return_referral: countTotalElement(assignments, 'return_referral'),
                    non_company: countTotalElement(assignments, 'non_company'),
                    tips: countTotalElement(assignments, 'tips'),
                    sale_tp: countTotalElement(assignments, 'sale_tp'),
                    other_tp: countTotalElement(assignments, 'other_tp'),
                    additional_gp: countTotalElement(assignments, 'additional_gp'),
                    additional_net: countTotalElement(assignments, 'additional_net'),
                }

                // TODO: payment lesenka
                let total_gross = 0
                const total_net = 0
                const total_profit = 0
                const total_penalty = 0
                const invoice = {
                    new: countTotalElement(assignments, 'new'),
                    return_referral: countTotalElement(assignments, 'return_referral'),
                    non_company: countTotalElement(assignments, 'non_company'),
                    tips: countTotalElement(assignments, 'tips'),
                    sale_tp: countTotalElement(assignments, 'sale_tp'),
                    other_tp: countTotalElement(assignments, 'other_tp'),
                    additional_gp: countTotalElement(assignments, 'additional_gp'),
                    additional_net: countTotalElement(assignments, 'additional_net'),
                }

                //TODO: additional

                Object.keys(invoice).forEach(key => {
                    total_gross += invoice[key as keyof typeof invoice]
                })

                const invoiceRecord = await findModelRecord('AgentReportInvoice', {
                    agent_report_pk: pk,
                    agent_pk,
                })

                if (invoiceRecord) {
                    await updateModelRecord('AgentReportInvoice', usePk(invoiceRecord), {
                        total_gross,
                        total_net,
                        total_profit,
                        total_penalty,
                        payload: {
                            total,
                            invoice,
                        },
                    })
                } else {
                    await new AgentReportInvoiceFactory().create({
                        agent_report_pk: pk,
                        agent_pk,
                        total_gross,
                        total_net,
                        total_profit,
                        total_penalty,
                        payload: {
                            total,
                            invoice,
                        },
                    })
                }
            }
        },
        addAgent: async function({ pk, agent_pk, department_pk }) {
            ///
            await new AgentReportInvoiceFactory().create({
                agent_report_pk: pk,
                agent_pk,
                department_pk,
            })
        },

        addAgentSale: async function({ pk, sale_pk, agent_pk, invoice_pk }) {
            await createModelRecord('AgentReportSale', {
                agent_invoice_pk: invoice_pk,
                sale_pk,
                agent_pk,
                //
                new: 0,
                return_referral: 0,
                non_company: 0,
                tips: 0,
                sale_tp: 0,
                other_tp: 0,
                additional_gp: 0,
                additional_net: 0,
                is_sale_tp_count: false,
                remark: '',
                special_services_fee: 0,
                airline_reimbursement_fee: 0,
                client_status_pk: randomElementPk(await findModelRecords('ClientStatus')),
            })
        },

        addAgentRevTktSale: async function({ pk, sale_pk, agent_pk, invoice_pk }) {
            await createModelRecord('AgentReportTicketing', {
                agent_invoice_pk: invoice_pk,
                sale_pk,
                agent_pk,
                type: AgentReportTicketingType.Manual,
                //
                amount: 0,
                remark: null,
            })
        },

        getVerificationInternalProfit: async function({ pk }) {
            return this.response({
                internal_profit: 10000,
                agent_pks: [],
            })
        },

        composeVerificationDepartmentInvoices: async function({ pk, agent_pks }) {
            const internal_profit = 10000

            const agent_bonus = internal_profit * 0.05
            const supervisor_bonus = agent_bonus * agent_pks.length + 0.02

            // TODO: clear assignments
            for (const agent_pk of agent_pks) {
                const agent = await findModelRecord('Agent', agent_pk)
                const position = await findModelRecord('Position', agent?.position_pk)

                // TODO: create invoice, create assignments, recalculate report
                if (position?.system_name === PositionName.Supervisor) {
                }

                if (position?.system_name === PositionName.Agent) {
                }
            }
        },

        composeExecutiveDepartmentInvoices: async function({ pk }) {
            //
        },

        composeRevTicketingDepartmentInvoices: async function({ pk }) {
            //
        },
    },
})
