import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

export default defineModelHandler<'AgentReportSale'>({
    actions: {
        removeSale: async function({ pk }) {
            await deleteModelRecord('AgentReportSale', pk)
        },

        save: async function({ data }) {
            for (const assignment of data) {
                await updateModelRecord('AgentReportSale', assignment.pk, assignment)
            }
        },

        addAdditional: async function({ pk, data }) {
            const record = await findModelRecord('AgentReportSale', pk)

            if (!record) {
                return
            }

            const result = {
                remark: record.remark ? record.remark : '' + ' /n ' + data.remark,
            }

            result[data.field] = record[data.field] + data.amount

            await updateModelRecord('AgentReportSale', pk, { ...result })
        },
    },

    searchFields: {},

    observers: {},
})
