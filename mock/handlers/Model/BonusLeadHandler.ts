import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

const modelName = 'BonusLead'

export default defineModelHandler<typeof modelName>({
    databaseModel: 'Lead',
    actions: {
        takeBonusLead: async function({ lead_pk }) {
            const agentPk = useMockAuth().pk

            const lead = await findModelRecord('Lead', (record) => {
                return usePk(record) === lead_pk
            })

            if (!lead) {
                throw new Error('No leads found')
            }

            await updateModelRecord('Lead', lead_pk, { executor_pk: agentPk })
        },
        assignAgentToBonusLead: async function({ lead_pks, agent_pk }) {
            if (!Array.isArray(lead_pks) || lead_pks.length === 0) {
                throw new Error('No leads found')
            }

            for (const lead_pk of lead_pks) {
                await findModelRecord('Lead', (record) => {
                    return usePk(record) === lead_pk
                })

                await updateModelRecord('Lead', lead_pk, { executor_pk: agent_pk })
            }
        },
    },
})
