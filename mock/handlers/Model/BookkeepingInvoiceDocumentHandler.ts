import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

export default defineModelHandler<'BookkeepingInvoiceDocument'>({
    actions: {
        create: async function({ document }) {
            // todo
        },
    },

    observers: {
        afterCreate: async function(document) {
            const otherDocuments = await findModelRecords('BookkeepingInvoiceDocument', (record) => {
                return record.invoice_pk === document.invoice_pk && record.id !== document.id
            })

            for (const otherDocument of otherDocuments) {
                await updateModelRecord('BookkeepingInvoiceDocument', usePk(otherDocument), {
                    is_active: false,
                })
            }
        },
    },
})
