import {
    deleteModelRecord,
    emitResourceHandlerEvent,
    findModelRecordOrFail,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import ReleasePostItemFactory from '~mock/factories/ReleasePost/ReleasePostItemFactory'

const modelName = 'ReleasePostItem'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ release_post_pk, title }) {
            await (new ReleasePostItemFactory()).create({
                release_post_pk,
                title,
                created_by_pk: useMockAuth().pk,
                is_empty: true,
                requested_by_pks: [],
            })

            if (release_post_pk) {
                await emitResourceHandlerEvent('ReleasePostReleasePostItemList', 'update', release_post_pk)
            }
        },
        remove: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },
        updateInfo: async function({ pk, description, condition, is_for_all, is_for_admin, is_exclude_admin,  requested_by_pks, ref_task_url  }) {
            await updateModelRecord('ReleasePostItemAdditionalInfo', pk, { condition, is_for_all, is_for_admin, is_exclude_admin })

            await updateModelRecord('ReleasePostItem', pk, { description, is_empty: !(condition || is_for_all || is_for_admin), requested_by_pks, ref_task_url })
        },
        updateTitle: async function({ pk, title  }) {
            await updateModelRecord(modelName, pk, { title })
        },
        attachFile: async function({ release_post_item_pk, file_pk }) {
            return this.response({ url: 'https://files.tmgbo.com/devStorage/bo/airline-report.xls' })
        },
        setOrder: async function({ order }) {
            let releasePostPk: PrimaryKey | undefined = undefined

            for (const { release_post_item_pk, position } of order) {
                await updateModelRecord(modelName, release_post_item_pk, { position })

                releasePostPk = (await findModelRecordOrFail('ReleasePostItem', release_post_item_pk)).release_post_pk
            }

            if (releasePostPk) {
                await emitResourceHandlerEvent('ReleasePostReleasePostItemList', 'update', releasePostPk)
            }
        },
    },
})
