import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import ContactRuleFactory from '~mock/factories/ContactRule/ContactRuleFactory'
import { ContactRuleType, ContactRuleValidationType } from '~/api/models/ContactRule/ContactRule'

const modelName = 'ContactRule'

export default defineModelHandler<'ContactRule'>({
    actions: {
        create: async function({ data }) {
            const rule = await (new ContactRuleFactory()).create(data)

            return this.response(rule)
        },
        update: async function({ pk, data }) {
            const rule = await updateModelRecord(modelName, pk, { ...data })

            return this.response(rule)
        },
        refresh: async function() {

        },
        test: async function({ data }) {
            const result = [] as string[]

            if (data.type === ContactRuleType.Email) {
                const emails = await findModelRecords('Email')

                if (data.validation_type === ContactRuleValidationType.ExactMatch) {
                    emails.forEach(email => {
                        if (email.value === data.value) {
                            result.push(email.value)
                        }
                    })
                }

                if (data.validation_type === ContactRuleValidationType.Regex) {
                    emails.forEach(email => {
                        const reg = new RegExp(data.value)

                        if (reg.test(email.value)) {
                            result.push(email.value)
                        }
                    })
                }
            }

            if (data.type === ContactRuleType.Phone) {
                const phones = await findModelRecords('Phone')

                if (data.validation_type === ContactRuleValidationType.ExactMatch) {
                    phones.forEach(phone => {
                        if (phone.value === data.value) {
                            result.push(phone.value)
                        }
                    })
                }

                if (data.validation_type === ContactRuleValidationType.Regex) {
                    phones.forEach(phone => {
                        const reg = new RegExp(data.value)

                        if (reg.test(phone.value)) {
                            result.push(phone.value)
                        }
                    })
                }
            }

            return this.response(result)
        },
    },

})
