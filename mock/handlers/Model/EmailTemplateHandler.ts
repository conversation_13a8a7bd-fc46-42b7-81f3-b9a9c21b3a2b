import { defineModelHand<PERSON> } from '~mock/utils/define'
import { wait } from '~/lib/Helper/PromiseHelper'

const modelName = 'EmailTemplate'

export default defineModelHandler<typeof modelName>({
    actions: {
        preview: async function({ name }) {
            await wait(1000)

            return this.response({
                html: `<p>Preview of ${name}</p>`,
                // url: 'https://google.com',
            })
        },
    },
})
