import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'ProductClientApprove'

export default defineModelHandler<typeof modelName>({
    actions: {
        getOfferPreview: async function() {
            return this.response({
                url: 'https://travelbusinessclass.com/best-deals/region/oceania',
            })
        },
        sendOffer: async function({ pk }) {
            await updateModelRecord('ProductClientApprove', pk, {
                is_sent_at: dateToUnixTimestamp(new Date()),
                is_viewed_at: dateToUnixTimestamp(new Date()),
                is_approved_at: dateToUnixTimestamp(new Date()),
            })
        },
    },
    searchFields: {},
    observers: {},
})
