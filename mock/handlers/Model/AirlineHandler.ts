import { define<PERSON>odel<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { usePk } from '~/composables/usePk'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'

export default defineModelHandler<'Airline'>({
    actions: {
        getSupportedForParsing: async function() {
            const airlines = await findModelRecords('Airline')
            const result = randomElements(airlines, randomInt(2, 4)).map(usePk)

            return this.response(result)
        },
    },

})
