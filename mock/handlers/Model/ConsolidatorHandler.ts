import { define<PERSON>odelHand<PERSON> } from '~mock/utils/define'
import ConsolidatorFactory from '~mock/factories/Consolidator/ConsolidatorFactory'
import { updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'Consolidator'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ data }) {
            const consolidator = await new ConsolidatorFactory().create({
                ...data,
                system_name: data.name.toLowerCase(),
            })

            return this.response(consolidator)
        },
        edit: async function({ pk, data }) {
            const consolidator = await updateModelRecord('Consolidator', pk, data)

            return this.response(consolidator)
        },
        delete: async function({ pk }) {
            await updateModelRecord('Consolidator', pk, { is_deleted: true })
        },
    },

})
