import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecordOrFail,
    findModelRecords,
    updateModelRecord,
    wherePk,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { composeResourcePk } from '~/composables/usePk'

const modelName = 'ChatMessage'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ message }) {
            const newMessage = await createModelRecord(modelName, {
                ...message,
                created_at: dateToUnixTimestamp(new Date()),
                created_by_pk: useMockAuth().pk,
            })

            await updateChatAdditionalInfo(newMessage.chat_pk)

            return this.response(newMessage)
        },
        update: async function({ pk, message }) {
            const newMessage = await updateModelRecord(modelName, pk, {
                ...message,
                updated_at: dateToUnixTimestamp(new Date()),
            })

            await updateChatAdditionalInfo(newMessage.chat_pk)

            return this.response(newMessage)
        },
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
        },
        pin: async function({ pk, pin }) {
            // some action which will pin or unpin message
        },
    },

    observers: {
        afterDelete: async function({ poll_pk }) {
            if (!poll_pk) {
                return
            }

            await deleteModelRecord('Poll', poll_pk)
        },
    },
})

async function updateChatAdditionalInfo(chatPk: PrimaryKey) {
    const infoPk = composeResourcePk('ChatAdditionalInfo', {
        pk: chatPk,
        auth_pk: '3',
    })

    await updateModelRecord('ChatAdditionalInfo', infoPk, await getChatAdditionalInfo(infoPk))
}

export async function getChatAdditionalInfo(infoPk: PrimaryKey) {
    const [chatPk] = infoPk.split(':')

    const messages = await findModelRecords('ChatMessage', {
        chat_pk: chatPk,
    })

    const info = await findModelRecordOrFail('ChatAdditionalInfo', wherePk(infoPk))

    const newMessages = !info?.last_read_at ? messages : messages.filter((message) => message.created_by_pk !== useMockAuth().pk && message.created_at > (info.last_read_at || 0))

    return {
        messages_count: messages.length,
        new_messages_count: newMessages.length,
        last_message_pk: tryUsePk(messages.at(-1)),
        last_read_at: Math.max(info?.last_read_at || 0, messages.at(-1)?.created_at || 0) || null,
        is_mentioned: newMessages.some((message) => message.agent_mention_pks?.includes(useMockAuth().pk)),
    }
}
