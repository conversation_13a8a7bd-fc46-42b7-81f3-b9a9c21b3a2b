import { defineModelHand<PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'Poll'

export default defineModelHandler<typeof modelName>({
    actions: {
        async vote({ pk, value }) {
            const record = await findModelRecordOrFail(modelName, pk)

            const result = structuredClone(record.result)

            const userPk = useMockAuth().pk

            result.pending = result.pending.filter(pk => pk !== userPk)

            result.voted.push({
                auth_pk: userPk,
                value,
            })

            await updateModelRecord(modelName, pk, {
                result,
            })
        },
    },
})
