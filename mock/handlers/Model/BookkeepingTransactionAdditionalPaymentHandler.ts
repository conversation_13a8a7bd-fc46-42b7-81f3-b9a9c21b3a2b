import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, findModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

export default defineModelHandler<'BookkeepingTransactionAdditionalPayment'>({
    actions: {
        update: async function({ pk, payload }) {
            await updateModelRecord('BookkeepingTransactionAdditionalPayment', pk, {
                payload,
            })
        },
    },

})
