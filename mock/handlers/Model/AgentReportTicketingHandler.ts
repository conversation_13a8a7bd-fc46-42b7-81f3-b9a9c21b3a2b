import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

export default defineModelHandler<'AgentReportTicketing'>({
    actions: {
        save: async function({ data }) {
            for (const assignment of data) {
                await updateModelRecord('AgentReportTicketing', assignment.pk, assignment)
            }
        },

        removeManual: async function({ pk }) {
            await deleteModelRecord('AgentReportTicketing', pk)
        },
    },
})
