import { defineModelHand<PERSON> } from '~mock/utils/define'
import {  updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { StateValue } from '~/api/models/CheckInReminder/CheckInReminder'

const modelName = 'CheckInReminder'

export default defineModelHandler<typeof modelName>({
    actions: {
        setDone: async function({ pk }) {
            await updateModelRecord(modelName, pk, { state: StateValue.Done })
        },
    },
})
