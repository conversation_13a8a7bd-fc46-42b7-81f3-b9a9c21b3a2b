import { defineModelHandler } from '~mock/utils/define'

const modelName = 'ClientAdditionalInfo'

export default defineModelHandler<typeof modelName>({
    actions: {
        getEnrichedInfo: async function({ pk, auth_pk }) {
            const info = {
                'email': '<EMAIL>',
                'email_valid': false,
                'email_provider': 'mock.com',
                'email_business': true,

                'phone': '+***********',
                'phone_valid': false,
                'phone_location': 'Moldova / Moldova',

                'google_contributor_url': 'https://maps.google.com/contributor',
                'google_cover_photo_url': 'https://lead-enrich-cdn.stage.tmgclick.com/test/avatars/ba5cc3cb-2a30-475a-bcfb-008837df13b9.jpg',
                'google_profile_photo_url': 'https://lead-enrich-cdn.stage.tmgclick.com/test/avatars/ba5cc3cb-2a30-475a-bcfb-008837df13b9.jpg',
                'google_apps': [
                    'Gmail',
                    'Drive',
                ],

                'whatsapp_profile_photo_url': 'https://lead-enrich-cdn.stage.tmgclick.com/test/avatars/ba5cc3cb-2a30-475a-bcfb-008837df13b9.jpg',

                'business_name': 'Vadim Creanga',
                'business_photo_url': 'https://lead-enrich-cdn.stage.tmgclick.com/test/avatars/ba5cc3cb-2a30-475a-bcfb-008837df13b9.jpg',
                'business_linkedin_url': 'http://www.linkedin.com/in/vadimcreangamock',
                'business_title': 'Business Owner',
                'business_headline': 'Passionate about leading innovative solutions in the tech industry. Driving sustainable growth and success.',
                'business_location': 'Sydney, New South Wales, Australia',
                'business_tags': [
                    'business_development',
                    'owner',
                    'racing',
                ],
            }

            return this.response({
                id: Number(pk),
                auth_pk,
                data: info,
                has_seen: false,
            })
        },
    },
})
