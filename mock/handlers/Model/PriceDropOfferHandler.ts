import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { PriceDropOfferStatus } from '~/api/models/PriceDrop/PriceDropOffer'

const modelName = 'PriceDropOffer'

export default defineModelHandler<typeof modelName>({
    actions: {
        approve: async function({ pk, remark }) {
            //do nothing
            await updateModelRecord(modelName, pk, {
                status: PriceDropOfferStatus.Approved,
                remark,
            })
        },
        decline: async function({ pk, remark }) {
            await updateModelRecord(modelName, pk, {
                status: PriceDropOfferStatus.Declined,
                remark,
            })
        },
        getSabreLogs: async function({ pk }) {
            const offer = await findModelRecordOrFail('PriceDropOffer', pk)

            const example = {
                seats: [
                    'Unknown Type: int',
                ],
                fare_base: {
                    fare_basis: [
                        'string',
                    ],
                    fare_calc_line: [
                        'string',
                    ],
                },
                total_fare: {
                    total_fare: {
                        amount: 1,
                        currency: 'EUR',
                    },
                    base_fare: {
                        amount: 1,
                        currency: 'EUR',
                    },
                },
                taxes: [
                    {
                        amount: 1,
                        currency: 'EUR',
                    },
                ],
            }

            if (offer) {
                return this.response({
                    logs: example,
                })
            }
        },

        markAsViewed: async function({ pks }) {
            for (const pk of pks) {
                await updateModelRecord('PriceDropOffer', pk, {
                    is_read: true,
                })
            }
        },
    },
    searchFields: {
        //
    },
})
