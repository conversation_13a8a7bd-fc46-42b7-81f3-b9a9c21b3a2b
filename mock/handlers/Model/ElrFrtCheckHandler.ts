import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecordOrFail, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import IssueSeeder from '~mock/seeds/models/Issue/IssueSeeder'
import { usePk } from '~/composables/usePk'
import { IssueCategory } from '~/api/models/Issue/Issue'
import type { ModelAttributes } from '~types/lib/Model'

const modelName = 'ElrFrtCheck'

export default defineModelHandler<typeof modelName>({
    actions: {
        takeRequest: async function({ pk }) {
            const cancellation = await findModelRecordOrFail('ElrFrtCheck', pk)

            const issue = await new IssueSeeder().create({
                model_name: 'Sale',
                model_pk: cancellation.sale_pk,
                category: IssueCategory.AirlineReimbursementCancellation,
            }, {
                withChat: true,
            })

            await updateModelRecord('ElrFrtCheck', pk, {
                request_pk: usePk(issue),
            })

            return this.response({
                request_pk: usePk(issue),
            })
        },
    },
    searchFields: {
        issue_status: async (record: ModelAttributes<'ElrFrtCheck'>) => {
            if (record.request_pk) {
                return (await findModelRecordOrFail('Issue', record.request_pk)).status
            }

            return ''
        },
    },
})
