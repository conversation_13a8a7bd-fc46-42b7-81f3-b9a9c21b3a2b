import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { useMockAuth } from '~mock/composables/useMockAuth'

const modelName = 'PriceQuote'

export default defineModelHandler<typeof modelName>({
    actions: {

        getAvailableAirlines: async function({ pk }) {
            const airlines = await findModelRecords('Airline')
            const randomAirlinesPks = randomElements(airlines, randomInt(2, 10)).map((airline) => usePk(airline))

            return this.response(randomAirlinesPks)
        },
        //

        viewedByAgent: async function({ pks }) {
            for (const pk of pks) {
                await updateModelRecord('PriceQuote', pk, {
                    is_viewed: true,
                    is_viewed_at: dateToUnixTimestamp(new Date()),
                })
            }
        },

        setHidden: async function({ pk, status }) {
            await updateModelRecord('PriceQuote', pk, {
                is_hidden: status,
            })
        },

        updateRemark: async function({ pk, mode, remark }) {
            if (mode === 'agent') {
                await updateModelRecord('PriceQuote', pk, {
                    agent_remark: remark,
                })
            }

            if (mode === 'client') {
                await updateModelRecord('PriceQuote', pk, {
                    client_remark: remark,
                })
            }
        },

        updateIsSoldByProvider: async function({ pk, is_sold_by_provider }) {
            await updateModelRecord('PriceQuote', pk, {
                is_sold_by_provider,
            })
        },

        updateActiveTill: async function({ pk, active_till }) {
            await updateModelRecord('PriceQuote', pk, {
                active_till,
            })
        },

        parseSegments: async function({ data }) {
            // @todo - delete it
            return this.response(
                {
                    'adult_count': 1,
                    'child_count': 0,
                    'infant_count': 0,
                    'segments': [
                        {
                            'id': '0',
                            'flight_id': 2027,
                            'line_raw': '1   DE 2027   L 24MAY   PHX FRA   740P 330P\u00a51',
                            'is_return': '0',
                            'number': 1,
                            'part_number': 0,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"PHX","to":"FRA","carrier":"DE","number":"2027","departure_at":1748140800,"arrive_at":1748179800,"plane":"339"},"meal":false,"meal_type":null,"flight_time":650,"flight_distance":"5637","gds":"Sabre","departure_terminal":"TERMINAL 4","arrival_terminal":"TERMINAL 1","is_technical":false,"parent_segment_number":false,"operated_by":""}',
                            'booking_class': 'L',
                        },
                        {
                            'id': 1,
                            'flight_id': 1462,
                            'line_raw': '2   LH*1462   M 25MAY   FRA LJU   915P 1025P',
                            'is_return': '0',
                            'number': 2,
                            'part_number': 0,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"FRA","to":"LJU","carrier":"LH","number":"1462","departure_at":1748200500,"arrive_at":1748204700,"plane":"CR9"},"meal":true,"meal_type":null,"flight_time":70,"flight_distance":"379","gds":"Sabre","departure_terminal":"TERMINAL 1","arrival_terminal":null,"is_technical":false,"parent_segment_number":false,"operated_by":"LUFTH CITYLINE"}',
                            'booking_class': 'M',
                        },
                        {
                            'id': 2,
                            'flight_id': 1463,
                            'line_raw': '3   LH*1463   H 04JUN   LJU FRA   655A 820A',
                            'is_return': 1,
                            'number': 3,
                            'part_number': 1,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"LJU","to":"FRA","carrier":"LH","number":"1463","departure_at":1749012900,"arrive_at":1749018000,"plane":"CR9"},"meal":true,"meal_type":null,"flight_time":85,"flight_distance":"379","gds":"Sabre","departure_terminal":null,"arrival_terminal":"TERMINAL 1","is_technical":false,"parent_segment_number":false,"operated_by":"LUFTH CITYLINE"}',
                            'booking_class': 'H',
                        },
                        {
                            'id': 3,
                            'flight_id': 2016,
                            'line_raw': '4   DE 2016   L 04JUN   FRA JFK   1145A 225P',
                            'is_return': 1,
                            'number': 4,
                            'part_number': 1,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"FRA","to":"JFK","carrier":"DE","number":"2016","departure_at":1749030300,"arrive_at":1749061500,"plane":"339"},"meal":false,"meal_type":null,"flight_time":520,"flight_distance":"3856","gds":"Sabre","departure_terminal":"TERMINAL 1","arrival_terminal":"TERMINAL 7","is_technical":false,"parent_segment_number":false,"operated_by":""}',
                            'booking_class': 'L',
                        },
                        {
                            'id': 4,
                            'flight_id': 135,
                            'line_raw': '5   B6 135   L 04JUN   JFK PHX   630P 900P',
                            'is_return': 1,
                            'number': 5,
                            'part_number': 1,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"JFK","to":"PHX","carrier":"B6","number":"135","departure_at":1749076200,"arrive_at":1749096000,"plane":"320"},"meal":false,"meal_type":null,"flight_time":330,"flight_distance":"2153","gds":"Sabre","departure_terminal":"TERMINAL 5","arrival_terminal":"TERMINAL 3","is_technical":false,"parent_segment_number":false,"operated_by":""}',
                            'booking_class': 'L',
                        },
                        {
                            'id': 5,
                            'flight_id': 135,
                            'line_raw': '6   B6 135   L 04JUN   JFK PHX   630P 900P',
                            'is_return': 1,
                            'number': 6,
                            'part_number': 1,
                            'pre_ticket_number': 1,
                            'pre_ticket_id': 'new_0',
                            'flight_class': 'economy',
                            'baggage_quantity': 0,
                            'baggage_weight': 70,
                            'additional': '{"flight":{"from":"JFK","to":"PHX","carrier":"B6","number":"135","departure_at":1749076200,"arrive_at":1749096000,"plane":"320"},"meal":false,"meal_type":null,"flight_time":330,"flight_distance":"2153","gds":"Sabre","departure_terminal":"TERMINAL 5","arrival_terminal":"TERMINAL 3","is_technical":false,"parent_segment_number":false,"operated_by":""}',
                            'booking_class': 'L',
                        },
                    ],
                    'raw_segments': '*I\u00ab\n\n     1 DE2027L 24MAY J PHXFRA SS1   740P  330P  25MAY S \/DCDE \/E\n     2 LH1462M 25MAY S FRALJU SS1   915P 1025P \/DCLH \/E\n    OPERATED BY LUFTH CITYLINE\n     3 LH1463H 04JUN W LJUFRA SS1   655A  820A \/DCLH \/E\n    OPERATED BY LUFTH CITYLINE\n     4 DE2016L 04JUN W FRAJFK SS1  1145A  225P \/DCDE \/E\n     5 B6 135L 04JUN W JFKPHX SS1   630P  900P \/DCB6 \/E\n\nVI*\u00ab\n\n       FLIGHT  DATE  SEGMENT DPTR  ARVL    MLS  EQP  ELPD MILES SM\n     1 DE 2027 24MAY PHX FRA  740P  330P\u00a51      339 10.50  5637  N\n    DEP-TERMINAL 4                 ARR-TERMINAL 1                 \n    CABIN-ECONOMY\n     2 LH*1462 25MAY FRA LJU  915P 1025P   RG   CR9  1.10   379  N\n    DEP-TERMINAL 1                 \n    *FRA-LJU OPERATED BY LUFTH CITYLINE\n    CABIN-ECONOMY\n     3 LH*1463 04JUN LJU FRA  655A  820A   RG   CR9  1.25   379  N\n                                   ARR-TERMINAL 1                 \n    *LJU-FRA OPERATED BY LUFTH CITYLINE\n    CABIN-ECONOMY\n     4 DE 2016 04JUN FRA JFK 1145A  225P        339  8.40  3856  N\n    DEP-TERMINAL 1                 ARR-TERMINAL 7                 \n    CABIN-ECONOMY\n     5 B6  135 04JUN JFK PHX  630P  900P        320  5.30  2153  N\n    DEP-TERMINAL 5                 ARR-TERMINAL 3                 \n    CABIN-ECONOMY',
                },
            )
        },

        track: async function({ pk, data }) {
            // create price drop
            await updateModelRecord('PriceQuoteAdditionalTrackingInfo', pk, {
                is_tracked_by_pks: [useMockAuth().pk],
            })
        },

        getTrackingInfo: async function({ pk }) {
            return this.response({
                price_delta: randomInt(10, 30),
            })
        },

        stopTrack: async function({ pk }) {
            await updateModelRecord('PriceQuoteAdditionalTrackingInfo', pk, { is_tracked_by_pks: []})
        },
    },
})
