import { define<PERSON><PERSON>l<PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
createModelRecord,
deleteModelRecord,
deleteModelRecords,
findModelRecord,
findModelRecordOrFail,
findModelRecords,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import {
    BookkeepingTransactionAction,
    BookkeepingTransactionStatus,
} from '~/api/models/Bookkeeping/BookkeepingTransaction'
import { randomElement } from '~/lib/Helper/ArrayHelper'
import { usePk } from '~/composables/usePk'
import { similarity } from '~/lib/Helper/StringHelper'
import type { ModelAttributes } from '~types/lib/Model'

export const recalculateTransactionBalance = async (transaction_pk: string) => {
    const transaction = await findModelRecord('BookkeepingTransaction', { id: Number(transaction_pk) })
    const assignments = await findModelRecords('BookkeepingTransactionAssignment', { transaction_pk })

    if (transaction && assignments.length) {
        let balance = transaction.amount * Number(transaction.direction)
        assignments.forEach(assignment => {
            balance += assignment.amount
        })

        await updateModelRecord('BookkeepingTransaction', usePk(transaction), {
            balance: balance,
        })
    }
}
export default defineModelHandler<'BookkeepingTransaction'>({
    actions: {
        create: async function({ transaction }) {
            const item = await createModelRecord('BookkeepingTransaction', {
                ...transaction,
                balance: transaction.amount * Number(transaction.direction),
                external_status: 'empty',
                status: BookkeepingTransactionStatus.New,
            })

            const gateway = await findModelRecord(
                'PaymentGateway',
                { id: Number(transaction.payment_gateway_pk) },
            )

            await createModelRecord('BookkeepingTransactionAdditionalInfo', {
                id: item.id,
                created_at: Math.floor(Date.now() / 1000),
                created_by_pk: usePk(randomElement(await findModelRecords('Agent'))),
            })

            if (gateway) {
                await createModelRecord('BookkeepingTransactionAdditionalPayment', {
                    available_refund_amount: undefined,
                    available_actions: [BookkeepingTransactionAction.Capture, BookkeepingTransactionAction.Authorize],
                    edit_enabled: true,
                    id: item.id,
                    payload: {
                        payload_type: gateway.payload_type,
                        sale_pk: null,
                        sale_card_pk: null,
                        data: null,
                    },
                })
            }

            return this.response(item)
        },

        update: async function({ pk, transaction }) {
            await updateModelRecord('BookkeepingTransaction', pk, {
                ...transaction,
                amount: transaction.amount,
                balance: transaction.amount * Number(transaction.direction),
                direction: transaction.direction,
                payment_gateway_pk: transaction.payment_gateway_pk,
            })

            const gateway = await findModelRecord(
                'PaymentGateway',
                { id: Number(transaction.payment_gateway_pk) },
            )

            if (gateway) {
                await updateModelRecord('BookkeepingTransactionAdditionalPayment', pk, {
                    payload: {
                        payload_type: gateway.payload_type,
                        sale_pk: null,
                        sale_card_pk: null,
                        data: null, //
                    },
                })
            }

            return
        },

        updateDescription: async function({ pk, description }) {
            await updateModelRecord('BookkeepingTransaction', pk, {
                description,
            })
        },

        updatePaymentGateway: async function({ pk, payment_gateway_pk }) {
            await updateModelRecord('BookkeepingTransaction', pk, {
                payment_gateway_pk,
            })
        },

        process: async function({ pk, action, amount }) {
            const transaction = await findModelRecord('BookkeepingTransaction', {
                id: Number(pk),
            })

            if (!transaction) {

            }

            // todo emulate actions
            const item = await updateModelRecord('BookkeepingTransaction', pk, {
                amount: transaction?.amount,
                balance: transaction?.amount ? transaction.amount * Number(transaction?.direction) : 0,
            })

            return this.response(item)
        },

        processManual: async function({ pk }) {
            const transaction = await findModelRecordOrFail('BookkeepingTransaction', pk)

            // @todo

            return this.response(transaction)
        },

        matchWithInvoice: async function({ invoice_pk }) {
            const invoice = await findModelRecord('BookkeepingInvoice', invoice_pk)

            const transactions = await findModelRecords('BookkeepingTransaction', (record) => {
                return (!!record?.description && !!invoice?.description) && similarity(record.description, invoice?.description) > 0.05
            })

            return this.response(transactions)
        },
    },

    observers: {
        afterDelete: async function(record) {
            const pk = usePk(record)

            await deleteModelRecords('ActivityLog', {
                model_name: 'BookkeepingTransaction',
                model_pk: pk,
            })

            await deleteModelRecord('BookkeepingTransactionAdditionalInfo', pk)
            await deleteModelRecord('BookkeepingTransactionAdditionalPayment', pk)

            await deleteModelRecords('BookkeepingTransactionAssignment', {
                transaction_pk: pk,
            })
        },
    },
})
