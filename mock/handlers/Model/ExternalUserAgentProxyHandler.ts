import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElement } from '~/lib/Helper/ArrayHelper'

const modelName = 'ExternalUserAgentProxy'

export default defineModelHandler<typeof modelName>({
    actions: {
        generateProxy: async function({}) {
            return this.response({
                protocol: 'http',
                host: 'gate.nodemaven.com',
                port: 8080,
                username: 'tbc_developer-country-any-sid-3ce7f0b84c694-filter-medium',
                password: 'b159a6ddb32a4',
            })
        },

        getProxyLocationOptions: async function({}) {
            const countries = await findModelRecords('Country')
            const cities = await findModelRecords('Iata')
            const regions = await findModelRecords('Region')

            const res = countries.map((country) => {
                return {
                    title: country.name,
                    value: country.code2,
                    regions: regions.slice(randomInt(0, 4), randomInt(5, 11)).map((region) => {
                        return {
                            title: region.name || region.code,
                            value: region.code,
                        }
                    }),
                    cities: cities.slice(randomInt(0, 20), randomInt(30, 50)).map((city) => {
                        return {
                            title: city.city_name || city.code,
                            value: city.code,
                            region: randomElement(regions).code,
                        }
                    }),
                }
            })

            return this.response({
                multilogin_countries: res,
                node_maven_countries: res,
            })
        },
    },
})
