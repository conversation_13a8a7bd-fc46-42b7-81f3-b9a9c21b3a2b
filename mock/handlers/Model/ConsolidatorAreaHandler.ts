import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { ofetch } from 'ofetch'
import { createHash } from 'crypto'
import { nullPropsToUndefined } from '~/lib/Helper/ObjectHelper'
import { throwError } from '~mock/lib/Helper/ErrorHelper'

const modelName = 'ConsolidatorArea'

export default defineModelHandler<typeof modelName>({
    actions: {
        consolidatorRequest: async function(data, _, event) {
            const config = useRuntimeConfig(event)

            const fetch = ofetch.create({
                baseURL: config.consolidatorTool.apiBase,
            })

            const timestamp = Math.floor(Date.now() / 1000)

            const secret = config.consolidatorTool.secret
            const token = createHash('sha256').update(`${timestamp}_${secret}`).digest('hex')

            try {
                const response = await fetch<any>(data.url, {
                    ...data.options,
                    params: {
                        ...data.options.params,
                        token,
                        timestamp,
                    },
                })

                return this.response(response)
            } catch (e: any) {
                throw throwError(event, 'Something went wrong', {
                    status: e?.status || 500,
                    data: e?.data || {},
                })
            }
        },
    },

})
