import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { createModelRecord, deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { useMockAuth } from '~mock/composables/useMockAuth'
import { TaskCategory } from '~/api/models/Task/TaskGroup'

export default defineModelHandler<'Task'>({
    actions: {
        create: async function(params) {
            await createModelRecord('Task', {
                task_group_pk: params.task_group_pk,
                description: params.description,
                created_at: dateToUnixTimestamp(new Date),
                expire_at: dateToUnixTimestamp(new Date) + Timespan.days(4).inSeconds,
                start_at: dateToUnixTimestamp(new Date),
                need_confirm: false,
                is_system: false,
                is_autocompleted: false,
                can_extend_expiration_time: false,
                can_set_expiration_time: false,
                handler: '',
                referer_pk: '',
            })
        },

        delete: async function({ pk }) {
            await deleteModelRecord('Task', pk)
        },

        update: async function({ pk, description }) {
            await updateModelRecord('Task', pk, {
                description,
            })
        },

        setExpirationTime: async function({ pk, expire_at }) {
            await updateModelRecord('Task', pk, {
                expire_at,
            })
        },

        extendExpirationTime: async function({ pk }) {
            const time = Timespan.hour().inSeconds

            await updateModelRecord('Task', pk, {
                expire_at: dateToUnixTimestamp(new Date) + time,
            })
        },

        delayStartTime: async function({ pk, start_at }) {
            const time = Timespan.hour().inSeconds

            await updateModelRecord('Task', pk, {
                start_at: start_at || (dateToUnixTimestamp(new Date) + time),
            })
        },

        changeCompleted: async function({ pk, status }) {
            await updateModelRecord('Task', pk, {
                completed_by_pk: status ? useMockAuth().pk : null,
                completed_at: status ? dateToUnixTimestamp(new Date) : null,
            })
        },

        changeExecutor: async function({ pk, executor_pk }) {
            await updateModelRecord('Task', pk, {
                executor_pk,
                department_pk: null,
                team_pk: null,
            })
        },

        changeDepartment: async function({ pk, department_pk }) {
            await updateModelRecord('Task', pk, {
                department_pk,
                executor_pk: null,
                team_pk: null,
            })
        },

        changeTeam: async function({ pk, team_pk }) {
            await updateModelRecord('Task', pk, {
                team_pk,
                executor_pk: null,
                department_pk: null,
            })
        },

        setFollowUpTaskStatus: async function(params) {
            await updateModelRecord('Task', params.task_pk, {
                completed_by_pk: useMockAuth().pk,
                completed_at: dateToUnixTimestamp(new Date),
            })
        },
    },

    searchFields: {
        task_group: async () => {
            return TaskCategory.CustomerSupport
        },
        is_completed: async (record) => {
            return !!record.completed_at
        },
        executor_pk: async (record) => {
            return record.executor_pk === useMockAuth().pk
        },
        department_pk: async (record) => {
            return record.department_pk === useMockAuth().department_pk
        },
        team_pk: async (record) => {
            return record.team_pk === useMockAuth().team_pk
        },
        assigned_by_pk: async (record) => {
            return record.id > 300
        },
        is_on_task_page: async () => {
            return true
        },
    },
})

