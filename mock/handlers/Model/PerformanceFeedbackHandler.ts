import { defineModelHand<PERSON> } from '~mock/utils/define'
import { createModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'PerformanceFeedback'

export default defineModelHandler<typeof modelName>({
    actions: {
        leaveFeedback: async function({ rating, remark, agent_pk }) {
            await createModelRecord('PerformanceFeedback', {
                rating: rating,
                remark: remark,
                created_at: Date.currentUnixTimestamp(),
                agent_pk: agent_pk,
            })
            //
        },
    },
})
