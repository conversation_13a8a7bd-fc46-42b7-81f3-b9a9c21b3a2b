import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { AgentReportInvoiceStatus } from '~/api/models/AgentReport/AgentReportInvoice'

export default defineModelHandler<'AgentReportInvoice'>({
    actions: {
        changeStatus: async function({ pk, data }) {
            await updateModelRecord('AgentReportInvoice', pk, { ...data })
        },
        changeAgentBeginnerStatus: async function({ pk, is_beginner }) {
            await updateModelRecord('AgentReportInvoice', pk, { is_beginner })
        },
        forceApprove: async function({ pk }) {
            await updateModelRecord('AgentReportInvoice', pk, { status: AgentReportInvoiceStatus.ApprovedByManager })
        },
        deleteInvoice: async function({ pk }) {
            await deleteModelRecord('AgentReportInvoice', pk)
        },
        changeSalary: async function({ pk, base_salary }) {
            await updateModelRecord('AgentReportInvoice', pk, { base_salary })
        },
        changeBonus: async function({ pk, bonus }) {
            await updateModelRecord('AgentReportInvoice', pk, { bonus })
        },
        composeExecutiveDepartmentInvoice: async function({ pk }) {
            //
        },
        composeRevTicketingDepartmentInvoice: async function({ pk }) {
            //
        },
    },
})
