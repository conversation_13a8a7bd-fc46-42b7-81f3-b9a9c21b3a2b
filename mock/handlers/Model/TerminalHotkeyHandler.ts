import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
createModelRecord,
deleteModelRecord,
emitResourceHandlerEvent,
findModelRecordOrFail,
findModelRecords,
updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { PrimaryKey } from '@/types'

const modelName = 'TerminalHotkey'

export default defineModelHandler<typeof modelName>({
    actions: {
        create: async function({ title, description, command, autorun, keys, consolidator_system_name }) {
            const alreadyExisted = (await findModelRecords(modelName, { consolidator_system_name }))
                .find((terminalHotkey) => terminalHotkey.keys.join() === keys.join())

            let pk: PrimaryKey

            if (alreadyExisted) {
                 pk = (await updateModelRecord(modelName, alreadyExisted._pk, { title, description, command, autorun, consolidator_system_name }))._pk
            } else {
                pk = (await createModelRecord(modelName, { title, description, command, autorun, keys, consolidator_system_name }))._pk

                await emitResourceHandlerEvent('TerminalHotkeyList', 'insert', useMockAuth().pk)
            }

            return this.response({
                pk,
            })
        },
        update: async function({ pk, title, description, command, autorun, keys }) {
            const terminalHotkey = await findModelRecordOrFail(modelName, pk)
            await updateModelRecord(modelName, pk, { title, description, command, autorun, keys, consolidator_system_name: terminalHotkey.consolidator_system_name })
        },
        delete: async function({ pk }) {
            await deleteModelRecord(modelName, pk)
            await emitResourceHandlerEvent('TerminalHotkeyList', 'delete', useMockAuth().pk)
        },
    },
    searchFields: {

    },
})
