import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import {
    createModelRecord,
    deleteModelRecord,
    findModelRecordOrFail,
    updateModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import { usePk } from '~/composables/usePk'

export default defineModelHandler<'ClientEmailData'>({
    actions: {
        create: async ({ client_pk, email }) => {
            const client = await findModelRecordOrFail('Client', client_pk)
            const emailModel = await createModelRecord('Email', { value: email })
            await createModelRecord('ClientEmailData', { client_pk: usePk(client), email_pk: usePk(emailModel) })
        },
        update: async ({ pk, email }) => {
            const clientEmailData = await findModelRecordOrFail('ClientEmailData', pk)
            await updateModelRecord('Email', clientEmailData.email_pk, { value: email })
        },
        remove: async ({ pk }) => {
            await deleteModelRecord('ClientEmailData', pk)
        },
        saveRemark: async ({ pk, remark }) => {
            await updateModelRecord('ClientEmailData', pk, { remark })
        },
    },
        searchFields: {},
        observers: {},
    },
)
