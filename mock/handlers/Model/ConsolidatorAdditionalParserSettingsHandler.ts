import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { faker } from '@faker-js/faker'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { usePk } from '~/composables/usePk'

export default defineModelHandler<'ConsolidatorAdditionalParserSettings'>({
    actions: {
        getRawData: async function(data) {
            const result = []
            for (let i = 0; i < 10; i++) {
                const row = []
                for (let r = 0; r < randomInt(6, 12); r++) {
                    const value = randomBoolean() ? faker.string.alphanumeric(5) : null
                    row.push(value)
                }
                result.push(row)
            }

            return this.response(result)
        },

        save: async function({ pk, report_pk, empty_lines, schema }) {
            // todo
            await updateModelRecord('ConsolidatorAdditionalParserSettings', pk, {
                empty_lines,
                schema,
            })

            // @todo You need to create a new version of the report from the current one and then return the pk of the new version. See how "save" action works in the "AirlineReportVersion" model.
            const versions = await findModelRecords('AirlineReportVersion', {
                report_pk,
                is_active: true,
            })

            let versionPk = '1'

            if (versions?.length) {
                versionPk = usePk(versions[versions.length - 1])
            }

            return this.response({
                version_pk: versionPk,
            })
        },
    },

})
