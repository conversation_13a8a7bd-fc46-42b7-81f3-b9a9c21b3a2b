import { define<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~mock/utils/define'
import { findModelRecords, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'EmailContactAttemptConversation'

export default defineModelHandler<typeof modelName>({
    actions: {
        setHidden: async function({
                                       pks,
                                       state,
                                   }) {
            for (const pk of pks) {
                await updateModelRecord(modelName, pk, { is_hidden: state })
            }
        },

        setFavorite: async function({
                                         pks,
                                         state,
                                     }) {
            for (const pk of pks) {
                await updateModelRecord(modelName, pk, { is_favorite: state })
            }
        },

        setRead: async function({
                                     pks,
                                     state,
                                 }) {
            for (const pk of pks) {
                await updateModelRecord(modelName, pk, {
                    read_by_executor_at: state ? dateToUnixTimestamp(new Date()) : null,
                    read_by_other_at: state ? dateToUnixTimestamp(new Date()) : null,
                })
            }
        },

        getRawMessageUrl: async function() {
            return this.response({
                url: 'https://travelbusinessclass.com/best-deals/region/oceania',
            })
        },

        search: async function({ search_query }) {
            const conversations = await findModelRecords('EmailContactAttemptConversation')

            const filtered = conversations.filter((conversation) => {
                return conversation.subject.includes(search_query)
            })

            return this.response({
                conversation_pks: filtered.map(usePk),
            })
        },
    },
})
