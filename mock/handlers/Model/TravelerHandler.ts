import { defineM<PERSON>l<PERSON>and<PERSON> } from '~mock/utils/define'
import { createModelRecord, deleteModelRecord, updateModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'

const modelName = 'Traveler'

export default defineModelHandler<typeof modelName>({
    actions: {
        createTraveler: async function({ data }) {
            if (!data.client_pk) {
                return
            }
            await createModelRecord(modelName, data)
        },
        updateTraveler: async function({ traveler_pk, data }) {
            await updateModelRecord(modelName, traveler_pk, {
                ...data,
            })
        },
        deleteTraveler: async function({ traveler_pk }) {
            await deleteModelRecord(modelName, traveler_pk)
        },
    },

})
