import { defineDriver } from 'unstorage'
import memoryDriver from 'unstorage/drivers/memory'

export const databaseInfo = {
    lastChange: 0,
    track: false,
}

export const FileSystemConcurrentStorageDriver = defineDriver(() => {
    const driver = memoryDriver()

    const writeChange = () => {
        if (!databaseInfo.track) {
            return
        }

        databaseInfo.lastChange = Math.max(databaseInfo.lastChange, Date.now())
    }

    const triggerChange = (promise: Promise<any>) => {
        if (!promise) {
            writeChange()

            return
        }

        return promise.then((result) => {
            writeChange()

            return result
        })
    }

    return {
        ...driver,
        name: 'fs-concurrent',
        async setItem(key, value, _opts) {
            return triggerChange(driver.setItem(key, value, _opts))
        },
        async removeItem(key, _opts) {
            return triggerChange(driver.removeItem(key, _opts))
        },
        async clear(base, _opts) {
            return triggerChange(driver.clear(base, _opts))
        },
        async dispose() {
            await trigger<PERSON>hange(driver.dispose())
        },
    }
})
