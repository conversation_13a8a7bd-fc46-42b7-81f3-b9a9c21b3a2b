import type { ResourceChangesResponse } from '~/service/ResourceCacheManager'

const tableName = '_ResourceChanges'

const storage = useDatabase()

type ResourceChange = ResourceChangesResponse['changes'][number]

export async function getResourceChanges() {
    return (await storage.getItem(tableName) as ResourceChange[]) ?? []
}

export async function recordResourceChange(data: ResourceChange) {
    const changes = await getResourceChanges()

    changes.push(data)

    await storage.setItem(tableName, changes)
}

export function getLastBlock() {
    return 1
}
