import type { H3Event } from 'h3'
import { H3Error } from 'h3'
import type { AnyObject } from '@/types'

export const throwError = (
    request: H3Event,
    message = 'Something went wrong',
    options: {
        status?: number
        data?: AnyObject,
    } = {},
): never => {
    const error = new H3Error(message)

    error.name = 'ApiError'

    error.data = {
        error: {
            type: 'Error',
            data: options.data || {},
        },
        message,
        success: false,
    }

    error.statusCode = options.status || 500
    error.statusMessage = message

    sendError(request, error, true)

    throw error
}
