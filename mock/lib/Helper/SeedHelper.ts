// 1, 2 ... n
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElement } from '~/lib/Helper/ArrayHelper'

export function items(n: number, offset = 0) {
    return Array.from({ length: n }, (_, i) => i + 1 + offset)
}

export function dateToUnixTimestamp(date: undefined): null
export function dateToUnixTimestamp(date: Date): number
export function dateToUnixTimestamp(date: Date | undefined) {
    if (date === undefined || date === null) {
        return null
    }

    return Math.floor((date.getTime()) / 1000)
}

export function randomEnumValue<T extends AnyObject>(enumObj: T): T[keyof T] {
    return randomElement(Object.values(enumObj))
}

export function withChance<TValue>(value: TValue | (() => TValue), chance?: number): TValue | null {
    return randomBoolean(chance) ? (
        // @ts-ignore
        typeof value === 'function' ? value() : value
    ) : null
}

export function randomBoolean(trueChance = 0.5) {
    return Math.random() < trueChance
}

export const createModelDates = <T extends string>(fields: T[]): { [Key in T]: number } => {
    const result: any = {}

    let prevField: string | undefined = undefined

    fields.forEach(field => {
        const date = !prevField ? new Date() : new Date(result[prevField] * 1000)
        const dateObj = new Date(date.setDate(date.getDate() + randomInt(1, 10)))
        result[field] = dateToUnixTimestamp(dateObj)
        prevField = field
    })

    return result
}
