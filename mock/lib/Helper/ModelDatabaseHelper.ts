// noinspection JSUnusedGlobalSymbols

import type {
    Definition,
    Model,
    ModelAttributes,
    ModelFields,
    ModelIdentification,
    PartialModelFields,
} from '~types/lib/Model'

import { useModelDefinition } from '~/composables/_core/useModelDefinition'
import { composePk } from '~/composables/usePk'
import type { ObjectValues, PickByType } from '~types/utils'
import {
    filterUndefined,
    meetsConditions,
    objectDifference,
    propIn,
    type SimpleFilterConditions,
} from '~/lib/Helper/ObjectHelper'
import { useEvent } from '~mock/composables/useEvent'
import resourceHandlers from '~mock/handlers/Resource/resourceHandlers'
import type {
    ResourceStorageWorkerData,
    ResourceStorageWorkerEvent,
    SystemStorageWorkerData,
} from '~/service/StorageWorker/StorageWorker'
import { relationsMap, resourceMap } from '~mock/plugins/relations-map'
import { ensureArray, filter, find, findIndex } from '~/lib/Helper/ArrayHelper'
import { useDatabase } from '~mock/composables/useDatabase'
import type { AnyObject } from '@casl/ability/dist/types/types'
import { getResourceChannelName, getSystemChannelName } from '~/lib/ResourceStorage/ResourceStorage'
import { recordResourceChange } from '~mock/lib/Helper/ResourceChangesHelper'
import { useMockAuth } from '~mock/composables/useMockAuth'
import { useMockWorkspace } from '~mock/composables/useMockWorkspace'
import { isMorphPrimaryKeyDefinition } from '~/lib/Model/ModelController'

export function createModelIdentification(resourceName: ResourceName, dataOrPk: AnyObject | PrimaryKey): ModelIdentification {
    const modelDefinition = useModelDefinition(resourceName as string)

    const pk = typeof dataOrPk === 'object' ? composePk(dataOrPk, modelDefinition?.pk ?? 'id') : dataOrPk

    if (!pk) {
        throw new Error('Could not create model identification. PK is undefined')
    }

    return {
        _pk: pk,
        _checksum: String(Date.now()),
        _memory_checksum: String(Date.now()),
        _is_archived: false,
        _has_archived_relations: false,
        _token: hash(`${resourceName}:${pk}`),
        _workspace: useMockWorkspace(),
    }
}

export function getModelIdentification(data: ModelIdentification): ModelIdentification {
    return {
        _pk: data._pk,
        _checksum: data._checksum,
        _memory_checksum: data._memory_checksum,
        _is_archived: data._is_archived,
        _has_archived_relations: data._has_archived_relations,
        _token: data._token,
    }
}

export async function getModelRecords<TModelName extends ModelName>(modelName: TModelName): Promise<ModelAttributes<TModelName>[]> {
    return (await useDatabase().getItem(modelName)) || []
}

export async function getModelRecord<TModelName extends ModelName>(modelName: TModelName, pk: PrimaryKey): Promise<ModelAttributes<TModelName> | undefined> {
    const records = await getModelRecords(modelName)

    return records.find((record) => record._pk === pk)
}

export async function getModelRecordOrFail<TModelName extends ModelName>(modelName: TModelName, pk: PrimaryKey): Promise<ModelAttributes<TModelName>> {
    const record = await getModelRecord(modelName, pk)

    if (!record) {
        throw new Error(`Record "${modelName}:${pk}" not found`)
    }

    return record
}

export type PrimaryKeyCondition = [PrimaryKey | PrimaryKey[], Definition.PrimaryKey]
export type FindConditions<TModelName extends ModelName> =
    'all'
    | 'first'
    | 'last'
    | PrimaryKey
    | PrimaryKeyCondition
    | ((record: ModelAttributes<TModelName>) => boolean)
    | { [key in keyof ModelAttributes<TModelName>]?: ModelAttributes<TModelName>[key] }

export function wherePk(equal: PrimaryKey | PrimaryKey[], pkDefinition: Definition.PrimaryKey = '_pk'): PrimaryKeyCondition | 'all' {
    if (equal === 'all') {
        return 'all'
    }

    return [equal, pkDefinition]
}

export async function findModelRecords<TModelName extends ModelName>(
    modelName: TModelName,
    conditions?: FindConditions<TModelName>,
): Promise<ModelAttributes<TModelName>[]> {
    const records = await getModelRecords(modelName)

    if (!conditions) {
        return records
    }

    if (conditions === 'all') {
        return records
    }

    if (conditions === 'first') {
        return [records[0]]
    }

    if (conditions === 'last') {
        return [records[records.length - 1]]
    }

    // noinspection SuspiciousTypeOfGuard
    if (typeof conditions === 'string') {
        return records.filter((record) => {
            return record._pk === conditions
        })
    }

    if (typeof conditions === 'function') {
        return records.filter(conditions)
    }

    if (Array.isArray(conditions)) {
        return records.filter((record) => {
            if (Array.isArray(conditions[0])) {
                const pk = composePk(record, conditions[1])

                if (!pk) {
                    return false
                }

                return conditions[0].includes(pk)
            }

            return composePk(record, conditions[1]) === conditions[0]
        })
    }

    return records.filter((record) => {
        return Object.keys(conditions).every((key) => {
            // @ts-ignore
            return record[key] === conditions[key]
        })
    })
}

export async function findModelRecord<TModelName extends ModelName>(modelName: TModelName, conditions: FindConditions<TModelName>): Promise<ModelAttributes<TModelName> | undefined> {
    const records = await findModelRecords(modelName, conditions)

    return records[0]
}

export async function findModelRecordOrFail<TModelName extends ModelName>(modelName: TModelName, conditions: FindConditions<TModelName>): Promise<ModelAttributes<TModelName>> {
    const record = await findModelRecord(modelName, conditions)

    if (!record) {
        throw new Error(`Record "${modelName}" not found: ${JSON.stringify(conditions)}`)
    }

    return record
}

export async function createModelRecord<TModelName extends ModelName>(
    modelName: TModelName,
    attributes: PartialModelFields<TModelName>,
): Promise<ModelAttributes<TModelName>> {
    const useAttributes = makeModelRecord(modelName, attributes)

    const record = {
        ...createModelIdentification(modelName, useAttributes),
        ...useAttributes,
    } as ModelAttributes<TModelName>

    const storage = useDatabase()

    const records = await getModelRecords(modelName)

    records.push(record)

    await storage.setItem(modelName, records)

    await emitResourceEvent('insert', modelName, record)

    await emitRelatedResourceEvents(modelName, record)

    const modelHandler = useModelHandler(modelName)

    if (modelHandler?.observers?.afterCreate) {
        await modelHandler.observers.afterCreate(record)
    }

    return record
}

export function makeModelRecord<TModelName extends ModelName>(
    modelName: TModelName,
    attributes: PartialModelFields<TModelName>,
): ModelFields<TModelName> {
    const useAttributes = structuredClone(filterUndefined(attributes))

    // Increment ====
    const pkDefinition = useModelDefinition(modelName as string).pk

    let incrementedField: string | undefined = undefined

    if (Array.isArray(pkDefinition)) {
        if (pkDefinition.includes('id')) {
            incrementedField = 'id'
        }
    } else {
        incrementedField = pkDefinition
    }

    if (incrementedField && hasIncrementField(modelName) && !propIn(incrementedField, useAttributes)) {
        // @ts-ignore
        useAttributes[incrementedField] = nextIncrementValue(modelName)
    }

    // ==============

    return {
        ...makeDefaultFields(modelName),
        ...useAttributes,
    } as ModelFields<TModelName>
}

export async function updateModelRecord<TModelName extends ModelName>(modelName: TModelName, pk: PrimaryKey, attributes: Partial<ModelFields<TModelName>>): Promise<ModelAttributes<TModelName>> {
    const storage = useDatabase()

    const records = await getModelRecords(modelName)

    const record = await getModelRecordOrFail(modelName, pk)

    const newRecord = {
        ...record,
        ...attributes,
    }

    Object.assign(newRecord, createModelIdentification(modelName, newRecord))

    const index = records.findIndex((record) => record._pk === pk)

    records[index] = newRecord

    await storage.setItem(modelName, records)

    const difference = objectDifference(record, newRecord)

    if (difference) {
        await emitResourceEvent('update', modelName, newRecord, difference)

        await emitRelatedResourceEvents(modelName, difference, record)

        const modelHandler = useModelHandler(modelName)

        if (modelHandler?.observers?.afterUpdate) {
            await modelHandler.observers.afterUpdate(newRecord, difference as any)
        }
    }

    return newRecord
}

export async function deleteModelRecord<TModelName extends ModelName>(modelName: TModelName, pk: PrimaryKey | PrimaryKey[]): Promise<void> {
    const storage = useDatabase()

    const records = await getModelRecords(modelName)

    const deletedItems = []

    if (Array.isArray(pk)) {
        pk.forEach((pk) => {
            const index = records.findIndex((record) => record._pk === pk)

            if (index === -1) {
                return // TODO: throw 404 error? or just ignore?
            }

            deletedItems.push(records[index])

            records.splice(index, 1)
        })
    } else {
        const index = records.findIndex((record) => record._pk === pk)

        if (index === -1) {
            return // TODO: throw 404 error? or just ignore?
        }

        deletedItems.push(records[index])

        records.splice(index, 1)
    }

    await storage.setItem(modelName, records)

    await emitOnDeleteEvents(modelName, deletedItems)
}

export async function deleteModelRecords<TModelName extends ModelName>(modelName: TModelName, conditions?: FindConditions<TModelName>) {
    const deletedItems = await findModelRecords(modelName, conditions)

    await deleteTableRecords(modelName, conditions)

    await emitOnDeleteEvents(modelName, deletedItems)

    return deletedItems
}

const emitOnDeleteEvents = async (modelName: ModelName, deletedItems: ModelAttributes[]) => {
    const modelDefinition = useModelDefinition(modelName as string)
    const pkFields = Array.isArray(modelDefinition.pk) ? modelDefinition.pk : [modelDefinition.pk]

    for (const item of deletedItems) {
        await emitResourceEvent('delete', modelName, item)

        await emitRelatedResourceEvents(
            modelName,
            {
                ...item,
                ...Object.fromEntries(pkFields.map((field) => [field, null])),
            },
            Object.fromEntries(pkFields.map((field) => [field, item[field as keyof typeof item]])),
        )
    }

    await emitResourceListEvents(modelName)

    if (deletedItems.length) {
        const modelHandler = useModelHandler(modelName)

        if (modelHandler?.observers?.afterDelete) {
            for (const item of deletedItems) {
                await modelHandler.observers.afterDelete(item)
            }
        }
    }
}

// ================ Events ===================

export const shouldEmitResourceEvents = ref(true)

export async function withoutResourceEvents<T>(callback: () => (T | Promise<T>)): Promise<T> {
    shouldEmitResourceEvents.value = false

    const result = await callback()

    shouldEmitResourceEvents.value = true

    return result
}

const emitPivotChange = async (pivotName: ModelPivotName, record: AnyObject) => {
    if (!shouldEmitResourceEvents.value) {
        return
    }

    const pkField = getPivotPkField(pivotName)

    if (!pkField) {
        throw new Error(`Pivot "${pivotName}" has no primary key`)
    }

    await emitResourceHandlerEvent(pivotName, 'update', composePk(record, pkField))
}

const emitResourceListEvents = async (modelName: ModelName) => {
    if (!shouldEmitResourceEvents.value) {
        return
    }

    const resourceName = `${modelName}List` as ResourceName

    const handler = resourceHandlers[resourceName]

    if (!handler) {
        return
    }

    await emitResourceHandlerEvent(resourceName, 'update', useMockWorkspace() + ':all')
}

async function emitRelatedResourceEvents(modelName: ModelName, changes?: Record<string, any>, oldRecord?: Record<string, any>) {
    if (!shouldEmitResourceEvents.value) {
        return
    }

    const modelDefinition = useModelDefinition(modelName)

    if (!changes || ensureArray(modelDefinition.pk).every(key => key in changes)) {
        await emitResourceListEvents(modelName)
    }

    if (!changes) {
        return
    }

    const relations = relationsMap[modelName]

    if (!relations) {
        throw new Error(`Relations map for model "${modelName}" not found`)
    }

    for (const item of relations) {
        // If all item.fields are in changes
        if (item.fields.every((field) => propIn(field, changes))) {
            if (item.triggerResource) {
                const resourceDefinition = useResourceDefinition(item.triggerResource)

                if (oldRecord) {
                    const oldPk = getPkFromForeignKeyDefinition(modelName, item.fields, oldRecord)

                    if (oldPk) {
                        const resourcePk = composePk({
                            id: oldPk,
                            auth_pk: useMockAuth().pk,
                            workspace: useMockWorkspace(), // @todo Check if this is correct, maybe we should get workspace from oldRecord
                        }, resourceDefinition.pk)

                        if (resourcePk) {
                            await emitResourceHandlerEvent(item.triggerResource, 'update', resourcePk)
                        }
                    }
                }

                const pk = getPkFromForeignKeyDefinition(modelName, item.fields, changes)

                if (pk) {
                    const resourcePk = composePk({
                        id: pk,
                        auth_pk: useMockAuth().pk,
                        workspace: useMockWorkspace(), // @todo Check if this is correct, maybe we should get workspace from oldRecord
                    }, resourceDefinition.pk)

                    if (resourcePk) {
                        await emitResourceHandlerEvent(item.triggerResource, 'update', resourcePk)
                    }
                }
            } else if (item.update && oldRecord) {
                for (const model in item.update) {
                    const values = Object.entries(item.update[model])

                    for (const [field, value] of values) {
                        const found = await findModelRecords(model as ModelName, {
                            [field]: oldRecord[field],
                        })

                        for (const record of found) {
                            await updateModelRecord(model as ModelName, record._pk, {
                                [field]: value,
                            })
                        }
                    }
                }
            }
        }
    }
}

const getPkFromForeignKeyDefinition = (modelName: ModelName, fk: Definition.PrimaryKey, data: AnyObject): PrimaryKey | undefined => {
    let pk: PrimaryKey

    fk = ensureArray(fk)

    if (fk.length === 1) {
        pk = data[fk[0]] as PrimaryKey
    } else if (fk.includes('model_name') && fk.includes('model_pk')) {
        if (data['model_name'] !== modelName) {
            return
        }

        pk = data['model_pk'] as PrimaryKey
    } else {
        // @todo Composite PK
        throw new Error('Composite PK is not supported')
    }

    return pk
}

export async function emitResourceHandlerEvent(resourceName: ResourceName, eventName: ResourceStorageWorkerEvent, pk: PrimaryKey = 'all') {
    const handler = resourceHandlers[resourceName]

    if (!handler) {
        throw new Error(`Resource handler "${resourceName}" not found`)
    }

    const record = await handler(resourceName, pk, {} as any)

    await emitResourceEvent(
        eventName,
        resourceName,
        record,
        record,
    )
}

export async function emitResourceEvent(event: ResourceStorageWorkerEvent, resource: ResourceName, attributes: Model.Identification & AnyObject, changes?: AnyObject) {
    if (!shouldEmitResourceEvents.value) {
        return
    }

    const channelName = getResourceChannelName(resource, attributes)

    if (!channelName) {
        console.error(`Resource doesn't have _token value`)

        return
    }

    const microTime = Date.now() / 1000

    useEvent(
        channelName,
        event,
        {
            identification: getModelIdentification(attributes),
            changes: changes || attributes,
            microTime,
        } satisfies ResourceStorageWorkerData,
    )

    const checksumChangedPayload: SystemStorageWorkerData['checksumChanged'] = {
        model: resource,
        pk: usePk(attributes),
        microTime,
        checksum: attributes._checksum,
        isTokenChanged: Boolean(changes && ('_token' in changes)),
    }

    await recordResourceChange(checksumChangedPayload)

    useEventQueued(
        getSystemChannelName(),
        'checksumChanged',
        checksumChangedPayload,
    )
}

// ======================

export function getPkCondition(modelName: ModelName, pkField: Definition.PrimaryKey, pk: PropertyKey): AnyObject {
    let conditions: AnyObject = {}

    if (isMorphPrimaryKeyDefinition(pkField)) {
        conditions = {
            model_name: modelName,
            model_pk: pk,
        }
    } else if (!Array.isArray(pkField)) {
        conditions = {
            [pkField]: pk,
        }
    } else {
        // @todo Composite foreign key
        throw new Error(`Composite key not supported for model "${modelName}"`)
    }

    return conditions
}

// ================= Increments =================

export function hasIncrementField(modelName: ModelName) {
    return incrementsStartStorage.has(modelName)
}

export function nextIncrementValue(modelName: ModelName): number {
    let value = incrementsStorage.get(modelName)

    if (!value) {
        value = incrementsStartStorage.get(modelName) || startIncrementFrom
    }

    incrementsStorage.set(modelName, value + 1)

    return value
}

const incrementsStorage = new Map<ModelName, number>()
const incrementsStartStorage = new Map<ModelName, number>()
const startIncrementFrom = 1

export async function initIncrementsStorage(modelName: ModelName) {
    const records = await getModelRecords(modelName) as AnyObject[]

    const definition = useModelDefinition(modelName as string)

    let pkDefinition: string

    if (Array.isArray(definition.pk)) {
        if (!definition.pk.includes('id')) {
            return
        }

        // @todo Create incremented fields

        pkDefinition = 'id'
    } else {
        pkDefinition = definition.pk

        if (pkDefinition !== 'id') {
            return
        }
    }

    let max = startIncrementFrom - 1

    for (const record of records) {
        const pk = record[pkDefinition]

        if (typeof record !== 'object') {
            throw new Error(`Database table "${modelName}" is broken. Clear it and try again`)
        }

        if (pk === undefined) {
            const message = `Model "${modelName}" can't have autoincrement. It has no primary key "${pkDefinition}"`

            console.error(message)
            console.error(record)

            throw new Error(message)
        }

        if (typeof pk !== 'number') {
            throw new Error(`Model "${modelName}" can't have autoincrement. It has not numeric primary key`)
        }

        if (pk > max) {
            max = pk
        }
    }

    incrementsStartStorage.set(modelName, max + 1)
}

export async function resetIncrementsStorage(modelName: ModelName) {
    incrementsStorage.delete(modelName)
    incrementsStartStorage.delete(modelName)
}

export async function resetAllIncrementsStorage() {
    incrementsStorage.clear()
    incrementsStartStorage.clear()
}

export function makeDefaultFields<TModelName extends ModelName>(modelName: TModelName) {
    const definition = useModelDefinition(modelName as string).fields.shape

    const result: any = {}

    Object.keys(definition).forEach((field) => {
        if (!definition[field].isNullable()) {
            return
        }

        result[field] = null
    })

    return result as PickByType<ModelFields<TModelName>, null>
}

export function mockOnlyTable(tableName: string) {
    return `_${tableName}`
}

// ================= Pivots =================

export type PivotFields<TModelPivotName extends ModelPivotName> = {
    [key in ObjectValues<Pick<Model.Relation.PivotDefinition<TModelPivotName>, 'parentKey' | 'childKey'>>]: PrimaryKey
}

export type PivotForeignFields<TModelPivotName extends ModelPivotName> = {
    [key in ObjectValues<Pick<Model.Relation.PivotDefinition<TModelPivotName>, 'childKey'>>]: PrimaryKey
}

export async function findPivotRecords<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, conditions?: Partial<PivotFields<TModelPivotName>>) {
    const tableName = mockOnlyTable(pivotName)

    if (!conditions) {
        return getTableRecords(tableName)
    }

    return findTableRecords(tableName, conditions)
}

export async function addPivotRecord<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, record: PivotFields<TModelPivotName>) {
    const tableName = mockOnlyTable(pivotName)

    const pkField = getPivotPkField(pivotName)

    if (Array.isArray(pkField)) {
        throw new Error(`Composite or Morph PK is not supported for pivot "${pivotName}"`)
    }

    const pk = record[pkField as keyof typeof record]

    if (!pk) {
        throw new Error(`Can't add pivot record without "${pkField}" field`)
    }

    const conditions = {
        [pkField]: pk,
    }

    // noinspection UnnecessaryLocalVariableJS
    const keyConditions = record // @todo

    const existingRecord = (await findPivotRecords(pivotName, keyConditions))

    if (existingRecord.length) {
        await updatePivotRecord(pivotName, conditions, record)
    } else {
        await addTableRecord(tableName, record)
    }

    await emitPivotChange(pivotName, record)

    return record
}

export async function syncPivotRecords<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, pk: PrimaryKey, records: PivotForeignFields<TModelPivotName>[]) {
    const tableName = mockOnlyTable(pivotName)

    const pkField = getPivotPkField(pivotName)

    if (Array.isArray(pkField)) {
        throw new Error(`Composite or Morph PK is not supported for pivot "${pivotName}"`)
    }

    await deleteTableRecords(tableName, {
        [pkField]: pk,
    })

    for (const record of records) {
        await addPivotRecord(pivotName, {
            ...record,
            [pkField]: pk,
        })
    }
}

export async function deletePivotRecords<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, conditions?: Partial<PivotFields<TModelPivotName>>) {
    const tableName = mockOnlyTable(pivotName)

    const records = await findPivotRecords(pivotName, conditions)

    await deleteTableRecords(tableName, conditions)

    for (const record of records) {
        await emitPivotChange(pivotName, record)
    }

    return records
}

export async function deletePivotRecordsWithForeignModels<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, conditions?: Partial<PivotFields<TModelPivotName>>) {
    const deletedRecords = await deletePivotRecords(pivotName, conditions)

    const resourceInfo = resourceMap[pivotName]

    if (!resourceInfo) {
        throw new Error(`Resource info for "${pivotName}" not found`)
    }

    const childKey = resourceInfo.childKey

    if (Array.isArray(childKey)) {
        throw new Error(`Composite or Morph PK is not supported for pivot "${pivotName}"`)
    }

    const modelName = resourceInfo.childModel

    for (const record of deletedRecords) {
        const pk = record[childKey]

        if (!pk) {
            console.warn(`Can't delete pivot record without "${childKey}" field`)
            continue
        }

        await deleteModelRecord(modelName, pk as PrimaryKey)
    }
}

export async function updatePivotRecord<TModelPivotName extends ModelPivotName>(pivotName: TModelPivotName, conditions: Partial<PivotFields<TModelPivotName>>, changes: Partial<PivotFields<TModelPivotName>>) {
    const tableName = mockOnlyTable(pivotName)

    const record = await updateTableRecord(tableName, conditions, changes)

    await emitPivotChange(pivotName, record)

    return record
}

const getPivotPkField = (pivotName: ModelPivotName) => {
    const pivotInfo = resourceMap[pivotName]

    if (!pivotInfo) {
        throw new Error(`Pivot map info "${pivotName}" not found. Check your model definition`)
    }

    return pivotInfo.pk
}

// ================= Custom Tables =================

export async function getTableRecords(tableName: string) {
    const records = await useDatabase().getItem(tableName) as AnyObject[]

    return records || []
}

export async function findTableRecords(tableName: string, conditions: SimpleFilterConditions) {
    const records = await getTableRecords(tableName)

    return filter(records, conditions)
}

export async function findTableRecord(tableName: string, conditions: SimpleFilterConditions) {
    const records = await findTableRecords(tableName, conditions)

    return records[0]
}

export async function setTableRecords(tableName: string, records: AnyObject[]) {
    await useDatabase().setItem(tableName, records)
}

export async function addTableRecord(tableName: string, record: AnyObject) {
    const records = await getTableRecords(tableName)

    records.push(record)

    await setTableRecords(tableName, records)

    return record
}

export async function updateTableRecord(tableName: string, conditions: SimpleFilterConditions, changes: AnyObject) {
    const records = await getTableRecords(tableName)

    const record = find(records, conditions)

    if (!record) {
        throw new Error(`Record not found in table "${tableName}": ${JSON.stringify(conditions)}`)
    }

    Object.assign(record, changes)

    await setTableRecords(tableName, records)

    return record
}

export async function deleteTable(tableName: string) {
    await useDatabase().removeItem(tableName)
}

export async function deleteTableRecords(tableName: string, conditions?: SimpleFilterConditions) {
    if (!conditions) {
        await deleteTable(tableName)

        return
    }

    const records = await getTableRecords(tableName)

    const filtered = records.filter(item => !meetsConditions(item, conditions))

    await setTableRecords(tableName, filtered)
}

export async function deleteTableRecord(tableName: string, conditions: SimpleFilterConditions) {
    const records = await getTableRecords(tableName)

    const index = findIndex(records, conditions)

    if (index === -1) {
        throw new Error(`Record not found in table "${tableName}": ${JSON.stringify(conditions)}`)
    }

    records.splice(index, 1)

    await setTableRecords(tableName, records)
}
