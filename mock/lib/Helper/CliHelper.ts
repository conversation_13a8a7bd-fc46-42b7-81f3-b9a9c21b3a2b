import { WebsocketEvent } from '../../composables/useEvent'
import { WebSocket } from 'ws'
import consola from 'consola'
import { ofetch } from 'ofetch'
import { config } from '../../../src-new/utils/config'

export async function callCliCommand(command: string, options: { [key: string]: any }, watch = false) {
    let wsConnection: WebSocket
    let connected = false

    if (watch) {
        consola.success('Listening for changes...')

        setInterval(() => {
            tryConnect()
        }, 1000)
    }

    const createWs = () => {
        wsConnection = new WebSocket(`ws://${config.mock.ws.host}:${config.mock.ws.port}`)

        wsConnection.addEventListener('message', (event) => {
            try {
                const { data, event: eventName } = JSON.parse(event.data.toString()) as unknown as WebsocketEvent

                if (eventName === 'log') {
                    // @ts-ignore
                    const log = consola[data.type]

                    log(data.message)
                } else if (watch && eventName === 'watch-cli') {
                    return run()
                }
            } catch (e) {
                //
            }
        })

        if (watch) {
            wsConnection.onopen = () => {
                // consola.success('Listening for changes...')
                connected = true
            }

            wsConnection.onerror = () => {
                connected = false
            }

            wsConnection.onclose = () => {
                if (!connected) {
                    return
                }

                connected = false

                // consola.warn('Connection lost. Trying to reconnect...')
            }
        }
    }

    const tryConnect = () => {
        if (wsConnection.readyState !== WebSocket.CLOSED && wsConnection.readyState !== WebSocket.CLOSING) {
            return
        }

        createWs()
    }

    createWs()

    const run = async () => {
        try {
            return await ofetch('http://localhost:8090/cli/' + command, {
                method: 'POST',
                body: options,
            })
        } catch (e: any) {
            consola.error(e.data?.message || 'Something went wrong. See mock server logs')
            consola.info(' - See mock server logs for more info about error')
        } finally {
            if (!watch) {
                wsConnection.close()
            }
        }
    }

    return run()
}
