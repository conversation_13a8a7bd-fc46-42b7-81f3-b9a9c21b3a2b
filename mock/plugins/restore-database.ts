import fs from 'fs'
import { restoreSnapshot } from 'unstorage'
import { green, red } from 'console-log-colors'
import { app } from '../../path.config'
import { useDatabase } from '~mock/composables/useDatabase'
import { databaseInfo } from '~mock/lib/Storage/FileSystemConcurrentStorageDriver'

const snapshotPath = app.mock.database.snapshotFile

export default defineNitroPlugin(async () => {
    if (fs.existsSync(snapshotPath)) {
        try {
            const data = JSON.parse(fs.readFileSync(snapshotPath, { encoding: 'utf-8' }).toString())

            delete data._snapshotCreatedAt

            await restoreSnapshot(useDatabase(), data, '')

            console.info(green('✔'), '[Database] Snapshot loaded')
        } catch (e) {
            console.error(red('✗'), 'Failed to load database snapshot:', e)
        }
    }

    databaseInfo.track = true
})
