import { WebSocketServer } from 'ws'
import { Queue } from '~/lib/DataStructure/Queue'

let wsServer = createWsServer()

function createWsServer() {
    const wsServer = new WebSocketServer({
        port: config.mock.ws.port,
        host: config.mock.ws.host,
        verifyClient: (info, done) => {
            done(true)
        },
    })

    wsServer.on('connection', function connection() {
        sendQueuedEvents()

        // dumpWsConnections()
    })

    wsServer.on('close', () => {
        // dumpWsConnections()
    })

    return wsServer
}

export const dumpWsConnections = () => {
    console.log('WebSocket connections:', wsServer.clients.size)
}

export default defineNitroPlugin(() => {
    //
})

export function disconnectWs() {
    wsServer.clients.forEach((ws) => {
        ws.terminate()
    })

    console.log('WebSocket is disconnected')
}

export function connectWs() {
    wsServer = createWsServer()

    console.log('WebSocket is connected')
}

export function sendWsEvent(data: AnyObject) {
    if (!wsServer.clients.size) {
        throw new Error('No WebSocket connection')
    }

    wsServer.clients.forEach((ws) => {
        ws.send(JSON.stringify(data))
    })
}

const eventsQueue = new Queue<AnyObject>()

export function sendWsEventQueued(data: AnyObject) {
    if (!wsServer.clients.size) {
        eventsQueue.enqueue(data)

        return
    }

    sendWsEvent(data)
}

function sendQueuedEvents() {
    if (eventsQueue.length) {
        while (eventsQueue.length) {
            sendWsEvent(eventsQueue.dequeue() as AnyObject)
        }
    }
}

export const waitForCliConnection = () => {
    let interval: NodeJS.Timeout

    return new Promise((resolve) => {
        interval = setInterval(() => {
            if (wsServer.clients.size >= 2) { // Self + cli
                clearInterval(interval)
                resolve(true)
            }
        }, 100)
    })
}
