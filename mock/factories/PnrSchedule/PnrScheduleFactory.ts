import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import segmentsData from '@/api/mock/table/data/segmentsData'
import { PnrScheduleType, SegmentRecordChangeType } from '~/api/models/PnrSchedule/PnrSchedule'

const lineRaws = segmentsData.map(item => item.raw_line)

const modelName = 'PnrSchedule'

type Attributes = Partial<ModelFields<typeof modelName>>

const generateItinerary = (pnr: string, changed = false) => {
    const segment = randomElement(lineRaws).replaceAll('  ', ' ').replaceAll('   ', ' ').replaceAll('    ', ' ')

    const parts = segment.split(' ')

    return {
        number: Number(parts[0]),
        operatingAirline: parts[1],
        flightNumber: parts[2],
        fareClass: parts[3],
        departureDate: parts[4],
        originCode: parts[5],
        destinationCode: parts[6],
        departureTime: parts[7],
        arrivalTime: parts[8],
        is_changed: randomBoolean() ? changed : null,
        change_type: changed ? randomElement([SegmentRecordChangeType.Changed, SegmentRecordChangeType.New]) : randomElement([SegmentRecordChangeType.Canceled, SegmentRecordChangeType.Unchanged]),
        pnr: pnr,
    }
}

export default class PnrScheduleFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const modelRecord = await createModelRecord(modelName, await this.make(attributes, options))

        return modelRecord
    }

    public async make(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const original_itinerary = []
        const updated_itinerary = []
        const pnrs: string[] = []

        const tkt_number = []

        const have_update = options.withUpdated || randomBoolean()

        for (let i = 0; i < randomInt(2, 5); i++) {
            tkt_number.push(faker.string.alphanumeric(6))
        }

        for (let i = 0; i < randomInt(1, 5); i++) {
            const new_pnr = faker.string.alphanumeric(8)
            pnrs.push(new_pnr)

            const changed =  options.withUpdated || (have_update ? randomBoolean(0.8) : false)
            original_itinerary.push(generateItinerary(new_pnr, false))

            if (have_update) {
                updated_itinerary.push(generateItinerary(new_pnr, changed))
            }
        }

        return makeModelRecord(modelName, {
            sale_pk: randomElementPk(await findModelRecords('Sale')),
            created_at: dateToUnixTimestamp(faker.date.recent()),
            updated_at: dateToUnixTimestamp(faker.date.recent()) + (6 * 1000),
            deadline_at: dateToUnixTimestamp(faker.date.soon()),
            original_itinerary,
            updated_itinerary: updated_itinerary.length ? updated_itinerary : null,
            tkt_number: randomBoolean() ? tkt_number : null,
            pnr: randomElement(pnrs),
            consolidator_area_pk: randomBoolean() ? randomElementPk(await findModelRecords('ConsolidatorArea')) : null,
            issue_pk: null,
            type: PnrScheduleType.Active,
            ...attributes,
        })
    }
}

type Options = {
    withUpdated?: boolean
}
