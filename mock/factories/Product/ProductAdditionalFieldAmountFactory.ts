import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord, nextIncrementValue } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { ProductFieldType } from '~/api/models/Product/ProductAdditionalFieldAmount'

const modelName = 'ProductAdditionalFieldAmount'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ProductAdditionalFieldAmountFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            amounts: [ { field_type: ProductFieldType.FARE, amount: 100 } ],
            ...attributes,
        })
    }
}

