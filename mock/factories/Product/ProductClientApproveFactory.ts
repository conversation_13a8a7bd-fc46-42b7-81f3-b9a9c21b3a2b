import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'ProductClientApprove'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ProductClientApproveFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const is_sent = randomBoolean()

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            product_pk: randomElementPk(await findModelRecords('Product')),
            is_sent_at: is_sent ? dateToUnixTimestamp(faker.date.past()) : null,
            is_viewed_at: is_sent ? dateToUnixTimestamp(faker.date.past()) : null,
            is_approved_at: is_sent ? dateToUnixTimestamp(faker.date.past()) : null,
            is_error: !is_sent,
            offer_url: is_sent ? faker.internet.url() : null,
            ...attributes,
        })
    }
}

