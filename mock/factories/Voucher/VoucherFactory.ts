import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, items, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { randomElement, randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { VoucherCreditWith, VoucherStatus } from '~/api/models/Voucher/Voucher'
import { faker } from '@faker-js/faker'

const modelName = 'Voucher'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class VoucherFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const consolidatorAreas = await findModelRecords('ConsolidatorArea')

        const voucherAreas = randomElements(consolidatorAreas, randomInt(9, 10))

        const is_sale = randomBoolean(.9)
        const sale_pk = randomElementPk(await findModelRecords('Sale'))
        const client_pk = randomElementPk(await findModelRecords('Client'))
        const fops = ['PAX', 'CC', 'Pax Wire']

        return makeModelRecord(modelName, {
            model_name: is_sale ? 'Sale' : 'Client',
            model_pk: is_sale ? sale_pk : client_pk,
            client_pk,

            voucher_id: `TBCWO${randomInt(1, 1000)}`,
            created_by_pk: randomElementPk(await findModelRecords('Agent')),
            executor_pk: randomElementPk(await findModelRecords('Agent')),
            pnr: items(randomInt(2, 6)).map(() => faker.string.alphanumeric(4).toUpperCase()),
            net_price: randomFloat(0.1, 9999.99, 2),
            emd: randomFloat(0, 1000),
            points_trade: randomFloat(0, 1000),
            fop: randomElement(fops),
            pcc: voucherAreas.map(item => usePk(item)),
            status: randomEnumValue(VoucherStatus),
            credit_with: randomEnumValue(VoucherCreditWith),
            voucher_price: randomFloat(0.1, 9999.99, 2),
            issued_at: dateToUnixTimestamp(faker.date.recent()),
            expires_at: dateToUnixTimestamp(faker.date.future()),
            sale_reflected_pk: randomElementPk(await findModelRecords('Sale')),
            is_sent: randomBoolean(), // randomEnumValue(VoucherDuration),
            description: faker.lorem.words(10),
            upgrade_amount: randomFloat(0, 1000),
            expected_amount_pk: null,

            issue_pk: null,
            is_verified: randomBoolean(),
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
