import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { randomElement, randomElements } from '~/lib/Helper/ArrayHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { VoucherCreditWith } from '~/api/models/Voucher/Voucher'
import { faker } from '@faker-js/faker'
import { usePk } from '~/composables/usePk'

const modelName = 'VoucherPnrAdditional'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    voucher_pk: PrimaryKey,
}

export default class VoucherPnrAdditionalFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const consolidatorAreas = await findModelRecords('ConsolidatorArea')
        const airlines = await findModelRecords('Airline')
        const passengers = await findModelRecords('SaleVersionPassenger')

        const voucherAreas = randomElements(consolidatorAreas, randomInt(1, 2))
        const selectedPassengers = randomElements(passengers, randomInt(1, 3))
        const selectedAirlines = randomElements(airlines, randomInt(1, 3))

        const fops = ['PAX', 'CC', 'Pax Wire']

        return makeModelRecord(modelName, {
            pnr: faker.string.alphanumeric(4).toUpperCase(),
            net_amount: randomFloat(0.1, 9999.99, 2),
            emd_amount: randomFloat(0, 1000),
            points_trade_amount: randomFloat(0, 1000),
            fop: randomElement(fops),
            pcc: voucherAreas.map(item => usePk(item)),
            credit_with: randomEnumValue(VoucherCreditWith),
            voucher_price: randomFloat(0.1, 9999.99, 2),
            upgrade_amount: randomFloat(0, 1000),
            passengers_pks: selectedPassengers.map(item => usePk(item)),
            airline_pks: selectedAirlines.map(item => usePk(item)),
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
