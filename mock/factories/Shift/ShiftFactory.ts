import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { ShiftType } from '~/api/models/Shift/Shift'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'Shift'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ShiftFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const departments = await findModelRecords('Department')

        return makeModelRecord(modelName, {
            from: '14:00',
            to: '19:00',
            points: faker.number.int({ min: 0, max: 200 }),
            shift_type: randomEnumValue(ShiftType),
            department_pk: randomElementPk(departments),
            //
            ...attributes,
        })
    }
}
