import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { PriceDropOfferStatus, PriceDropOfferType } from '~/api/models/PriceDrop/PriceDropOffer'

const modelName = 'PriceDropOffer'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    check_pk: PrimaryKey,
}

export default class PriceDropOfferFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        const modelRecord = await createModelRecord(modelName, await this.make(attributes))

        return modelRecord
    }

    public async make(
        attributes: Attributes,
    ) {
        const adult_count = randomInt(1, 5)
        const child_count = randomBoolean() ? randomInt(0, adult_count) : 0
        const infant_count = randomBoolean() ? randomInt(0, adult_count - child_count) : 0

        const net_price = randomFloat(1000, 3000)
        const commission = randomFloat(10, 30)

        return makeModelRecord(modelName, {
            net_price,
            commission,
            price_drop_price: net_price - commission,
            issuing_fee: randomFloat(0, 15),

            snap: randomBoolean() ? faker.string.alphanumeric(4) : null,
            consolidator_area_pk: randomElementPk(await findModelRecords('ConsolidatorArea')),
            pax_count: {
                adult_count,
                child_count,
                infant_count,
            },
            status: PriceDropOfferStatus.Pending,
            remark: null,
            is_read: false,
            price_modifier: randomBoolean() ? faker.string.alphanumeric(3) : null,
            type: randomEnumValue(PriceDropOfferType),
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
