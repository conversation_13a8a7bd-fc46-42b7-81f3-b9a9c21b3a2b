import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import segmentsData from '@/api/mock/table/data/segmentsData'

const lineRaws = segmentsData.map(item => item.raw_line)

const modelName = 'PriceDropCheck'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PriceDropCheckFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const modelRecord = await createModelRecord(modelName, await this.make(attributes))

        return modelRecord
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const sales = await findModelRecords('Sale')

        const net_price = randomFloat(1000, 3000)
        const commission = randomFloat(10, 30)

        const ticket_info = []

        for (let i = 0; i < randomInt(1, 5); i++) {
            ticket_info.push(randomElement(lineRaws))
        }

        return makeModelRecord(modelName, {
            sale_pk: randomElementPk(sales),
            is_voidable: randomBoolean(),
            is_refundable: randomBoolean(),
            voidable_till: dateToUnixTimestamp(faker.date.future()),
            refundable_till: dateToUnixTimestamp(faker.date.future()),
            net_price,
            commission,
            price_drop_price: net_price - commission,
            issuing_fee: randomFloat(0, 15),

            ticket_info,
            pnr: faker.string.alphanumeric(6),
            consolidator_area_pk: randomElementPk(await findModelRecords('ConsolidatorArea')),

            token: faker.string.alphanumeric(66),
            ...attributes,
        })
    }
}

type Options = {
    //
}
