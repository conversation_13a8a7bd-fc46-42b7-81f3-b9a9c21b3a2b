import type { ModelAttributes, ModelFields } from '~types/lib/Model'

export default abstract class Factory<TModelName extends ModelName> {
    public abstract readonly relatedModels: ModelName[]

    public abstract create(
        attributes?: Partial<ModelFields<TModelName>>,
        options?: AnyObject,
    ): Promise<ModelAttributes<TModelName>>

    public abstract make(
        attributes?: Partial<ModelFields<TModelName>>,
        options?: AnyObject,
    ): Promise<ModelFields<TModelName>>
}
