import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomElement } from '~/lib/Helper/ArrayHelper'

const modelName = 'TerminalHotkey'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    keys: string[]
}

export default class TerminalHotkeyFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        const consolidators = await findModelRecords('Consolidator')

        return makeModelRecord(modelName, {
            autorun: faker.datatype.boolean(),
            consolidator_system_name: randomElement(consolidators).system_name,
            command: 'qweqweqweq',
            description: faker.word.words(10),
            title: faker.word.words(1),
            ...attributes,
        })
    }
}
