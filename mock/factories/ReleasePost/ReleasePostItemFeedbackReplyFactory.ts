import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'

const modelName = 'ReleasePostItemFeedbackReply'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class ReleasePostItemFeedbackReplyFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return await createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            agent_pk: randomElementPk(await findModelRecords('Agent')),
            release_post_item_feedback_pk: randomElementPk(await findModelRecords('ReleasePostItemFeedback')),
            created_at: Date.now(),
            message: faker.lorem.words(15),
            ...attributes,
        })
    }
}
