import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'

const modelName = 'ReleasePostItemFeedback'

type Attributes = Partial<ModelFields<typeof modelName>> & { release_post_item_pk: PrimaryKey }

export default class ReleasePostItemFeedbackFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            points: faker.number.int({ min: 1, max: 5 }),
            remark: faker.lorem.sentence(),
            created_by_pk: randomElementPk(await findModelRecords('Agent')),
            created_at: dateToUnixTimestamp(new Date()),
            ...attributes,
        })
    }
}
