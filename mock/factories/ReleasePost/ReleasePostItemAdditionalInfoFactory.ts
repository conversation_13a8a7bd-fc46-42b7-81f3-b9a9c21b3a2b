import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'ReleasePostItemAdditionalInfo'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class ReleasePostItemAdditionalInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            condition: null,
            is_for_all: true,
            is_for_admin: false,
            is_exclude_admin: false,
            ...attributes,
        })
    }
}
