import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    getModelRecords,
    makeModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { pluckPks, randomElementPk } from '~/lib/Helper/ArrayHelper'
import ReleasePostItemAdditionalInfoFactory from '~mock/factories/ReleasePost/ReleasePostItemAdditionalInfoFactory'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

const modelName = 'ReleasePostItem'

type Attributes = Partial<ModelFields<typeof modelName>> & { release_post_pk: PrimaryKey }

export default class ReleasePostItemFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {
        additionalInfo: new ReleasePostItemAdditionalInfoFactory(),
    }

    public async create(
        attributes: Attributes,
    ) {
        const item = await createModelRecord(modelName, await this.make(attributes))

        await this.factories.additionalInfo.create({
            id: item.id,
        })

        return item
    }

    public async make(
        attributes: Attributes,
    ) {
        const agents = (await getModelRecords('Agent')).splice(1, 4)

        return makeModelRecord(modelName, {
            title: faker.lorem.sentence(),
            description: faker.lorem.paragraph(),
            created_by_pk: randomElementPk(await findModelRecords('Agent')),
            is_empty: randomBoolean(),
            requested_by_pks: pluckPks(agents),
            ref_task_url: 'https://tbc-it.atlassian.net/browse/TD-6821',
            position: 0,
            ...attributes,
        })
    }
}
