import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import type { PrimaryKey } from '@/types'

const modelName = 'GamblingLotBet'

type Attributes = Partial<ModelFields<typeof modelName>> & { lot_pk: PrimaryKey, lot_option_pk: PrimaryKey }

export default class GamblingLotBetFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            id: nextIncrementValue('GamblingLotBet'),
            agent_pk: randomElementPk(await findModelRecords('Agent')),
            bet_amount: randomInt(0, 10000),
            created_at: dateToUnixTimestamp(faker.date.past()),

            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
