import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, randomBoolean, withChance } from '~mock/lib/Helper/SeedHelper'
import { randomElement, randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'

const modelName = 'Task'

type Attributes = Partial<ModelFields<typeof modelName>> & {
    task_group_pk: PrimaryKey,
}

export default class TaskFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const leads = await findModelRecords('Lead')
        const agents = await findModelRecords('Agent')

        let executor_pk = null
        let team_pk = null
        let department_pk = null
        let recommended_for_department_pks = []

        const assignField = randomElement(['executor', 'department', 'team'])
        const departments = await findModelRecords('Department')

        if (assignField === 'executor') {
            executor_pk = withChance(randomElementPk(agents))
        } else if (assignField === 'department') {
            department_pk = withChance(randomElementPk(departments))
        } else {
            const teams = await findModelRecords('Team')
            team_pk = withChance(randomElementPk(teams))
        }

        const start_at = randomBoolean() ? faker.date.recent({ days: 3 }) : faker.date.soon({ days: 3 })

        // is needed to make expire_at and completed_at fields always later than start_at
        const addDaysToDate = (days: number): Date => {
            const newDate = new Date(start_at)
            newDate.setDate(newDate.getDate() + days)

            return newDate
        }

        recommended_for_department_pks = [randomElementPk(departments)]

        return makeModelRecord(modelName, {
            description: faker.lorem.sentence(5),
            is_system: randomBoolean(),
            is_autocompleted: randomBoolean(),
            is_high_priority: randomBoolean(),
            need_confirm: randomBoolean(),
            can_set_expiration_time: randomBoolean(),
            can_extend_expiration_time: randomBoolean(),
            executor_pk,
            team_pk,
            department_pk,
            recommended_for_department_pks: recommended_for_department_pks,
            assigned_by_pk: withChance(randomElementPk(agents)),

            //

            created_at: dateToUnixTimestamp(start_at),
            start_at: withChance(dateToUnixTimestamp(start_at)),
            disabled_at: null,
            expire_at: dateToUnixTimestamp(addDaysToDate(Math.floor(Math.random() * 10))),
            completed_at: withChance(dateToUnixTimestamp(addDaysToDate(Math.floor(Math.random() * 10))), 0.7),
            handler: faker.string.sample(), // @todo replace to task_group_pk
            referer_pk: randomElementPk(leads), // @todo remove on integration process

            //
            ...attributes,
        })
    }
}
