import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields, ModelLinkIdentification } from '~types/lib/Model'
import { toHumanPhrase } from '~/lib/Helper/StringHelper'
import { faker } from '@faker-js/faker'
import TaskFactory from '~mock/factories/Task/TaskFactory'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { TaskCategory } from '~/api/models/Task/TaskGroup'
import { randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'TaskGroup'

type Attributes = Partial<ModelFields<typeof modelName>> & ModelLinkIdentification

type Options = {
    withTasks?: boolean | number,
    withFollowupTasks?: boolean,
}

export default class TaskGroupFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public factories = {
        task: new TaskFactory(),
    }

    public async create(
        attributes: Attributes,
        options?: Options,
    ) {
        const group = await createModelRecord(modelName, await this.make(attributes))

        const groupPk = usePk(group)

        if (options?.withTasks) {
            const taskCount = typeof options.withTasks === 'number' ? options.withTasks : 3

            for (let i = 0; i < taskCount; i++) {
                await this.factories.task.create({
                    task_group_pk: groupPk,
                })
            }
        }

        if (options?.withFollowupTasks) {
            const groupPk = usePk(group)

            await this.factories.task.create({
                task_group_pk: groupPk,
                handler: 'FollowUpIntro',
                description: 'Follow-up intro',
            })
        }

        return group
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            category: randomEnumValue(TaskCategory),
            name: toHumanPhrase(attributes.category ?? faker.lorem.words(randomInt(1, 4))),

            //
            ...attributes,
        })
    }
}
