import type { ModelFields } from '~types/lib/Model'
import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { PaymentGatewayPayloadType, PaymentGatewaySystemName } from '~/api/models/PaymentGateway/PaymentGateway'

const modelName = 'PaymentGateway'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PaymentGatewayFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            name: faker.string.alphanumeric(10),
            abbreviation: faker.string.alphanumeric(4),
            system_name: randomEnumValue(PaymentGatewaySystemName),
            payload_type: randomEnumValue(PaymentGatewayPayloadType),
            is_default: randomBoolean(),
            is_enabled: randomBoolean(),
            ...attributes,
        })
    }
}
