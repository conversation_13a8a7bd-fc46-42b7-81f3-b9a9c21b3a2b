import type { ModelFields } from '~types/lib/Model'
import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'

const modelName = 'PaymentGatewayBalanceAdditional'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PaymentGatewayBalanceAdditionalFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            target_ps: randomInt(0, 100),
            amount: randomFloat(100, 5000),
            amount_ps: randomFloat(0, 100),
            ...attributes,
        })
    }
}
