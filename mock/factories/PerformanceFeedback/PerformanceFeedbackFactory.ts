import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { dateToUnixTimestamp, withChance } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { getRangeThisYear } from '@/lib/core/helper/DateHelper'

const modelName = 'PerformanceFeedback'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PerformanceFeedbackFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const currentYear = new Date().getFullYear()

        const startOfYear = new Date(currentYear, 0, 1)
        const endOfYear = new Date(currentYear, 11, 31)

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            agent_pk: randomElementPk(await findModelRecords('Agent')),
            created_at: dateToUnixTimestamp(faker.date.between({ from: startOfYear, to: endOfYear })),
            rating: randomInt(1, 5),
            remark: withChance(faker.lorem.sentences(2), 0.7),

            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
