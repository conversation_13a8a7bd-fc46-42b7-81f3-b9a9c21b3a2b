import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord, nextIncrementValue } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'

const modelName = 'PerformanceFeedbackEvent'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PerformanceFeedbackEventFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const currentYear = new Date().getFullYear()

        const startOfYear = new Date(currentYear, 0, 1)
        const endOfYear = new Date(currentYear, 11, 31)

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            event_date: dateToUnixTimestamp(faker.date.between({ from: startOfYear, to: endOfYear })),
            title: faker.lorem.words(2),
            marker_hex: '#3B76F6',
            description: faker.lorem.sentence(1),
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
