import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { Gender } from '~types/enums/Gender'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { TicketPreferenceCategory } from '~/api/models/TicketExtraPreference/TicketExtraPreference'

const modelName = 'Traveler'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class TravelerFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const frequentFlyersNumber = randomInt(1, 3)
        const frequentFlyer = []

        for (let i = 0; i < frequentFlyersNumber; i++) {
            const airlinePk = randomElementPk(await findModelRecords('Airline'))

            frequentFlyer.push({
                airline_pk: airlinePk,
                frequent_flyer_number: '12312A',
            })
        }

        const names = {
            first_name: faker.person.firstName(),
            last_name: faker.person.lastName(),
            middle_name: faker.person.middleName(),
        }

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            ...names,
            birth_date: dateToUnixTimestamp(faker.date.past()),
            gender: randomEnumValue(Gender),
            client_pk: randomElementPk(await findModelRecords('Client')),
            known_traveler_number: withChance(faker.string.alphanumeric(5), 0.5),
            global_entry_number: withChance(faker.string.alphanumeric(5), 0.5),
            tsa_precheck_number: withChance(faker.string.alphanumeric(5), 0.5),
            frequent_flyer: frequentFlyer,
            special_assistance: randomElementPk(await findModelRecords('TicketExtraPreference', {
                category: TicketPreferenceCategory.Special,
                })),
            seat_preference_pk: randomElementPk(await findModelRecords('TicketExtraPreference', {
                category: TicketPreferenceCategory.Seat,
            })),
            remark: faker.lorem.sentence(4),

            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
