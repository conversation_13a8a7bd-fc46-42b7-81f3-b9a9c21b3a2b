import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'
import type z from 'zod'
import { PreTicketSellType, SegmentType } from '~/api/models/PriceQuote/PriceQuote'
import { items, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { randomElement } from '~/lib/Helper/ArrayHelper'
import segmentsData from '@/api/mock/table/data/segmentsData'
import type { PriceQuoteSegment } from '~/api/models/PriceQuote/PriceQuoteAdditionalSegments'
import { faker } from '@faker-js/faker'

const modelName = 'PriceQuoteAdditionalSegments'

const lineRaws = segmentsData.map(item => item.raw_line)

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class PriceQuoteAdditionalSegmentsFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public makeSegment(): z.infer<typeof PriceQuoteSegment> {
        return {
            id: randomInt(1, 10000),
            type: randomEnumValue(SegmentType),
            // flight_id: randomInt(1, 10000),
            is_return: randomBoolean(),
            group_index: randomInt(1, 4),
            is_private: randomBoolean(),
            pre_ticket_sell_type: randomEnumValue(PreTicketSellType),
            line_raw: randomElement(lineRaws),
            operated_by: randomBoolean() ? faker.string.sample(5) : null,
        }
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            // items: this.makeSegment(randomInt(1, 5)),
            items: items(randomInt(1, 5)).map(this.makeSegment),
            // @ts-ignore @todo
            auth_pk: '3',
        })
    }
}
