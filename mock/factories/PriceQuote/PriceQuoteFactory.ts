import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import PriceQuoteAdditionalSummaryFactory from '~mock/factories/PriceQuote/PriceQuoteAdditionalSummaryFactory'
import { ItineraryType } from '~/api/models/Lead/Lead'
import PriceQuoteAdditionalSegmentsFactory from '~mock/factories/PriceQuote/PriceQuoteAdditionalSegmentsFactory'
import { PqTourFareType } from '~/api/models/PriceQuote/PriceQuote'
import PriceQuoteAdditionalTrackingInfoFactory from '~mock/factories/PriceQuote/PriceQuoteAdditionalTrackingInfoFactory'

const modelName = 'PriceQuote'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class PriceQuoteFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        const modelRecord = await createModelRecord(modelName, await this.make(attributes))

        await new PriceQuoteAdditionalSummaryFactory().create({
            id: modelRecord.id,
        })

        await new PriceQuoteAdditionalSegmentsFactory().create({
            id: modelRecord.id,
        })

        await new PriceQuoteAdditionalTrackingInfoFactory().create({
            id: modelRecord.id,
        })

        return modelRecord
    }

    public async make(
        attributes: Attributes = {},
    ) {
        const created_at = randomBoolean() ? faker.date.recent({ days: 3 }) : faker.date.recent({ days: 5 }) // TODO when disable edit
        const has_fake_segments = randomBoolean()

        const is_sent = randomBoolean()
        const is_viewed = is_sent ? randomBoolean() : false

        const sales = randomBoolean(0.15) ? await findModelRecords('Sale') : []
        const sale_pks = sales.length ? randomElements(sales, randomInt(1, 3)).map(sale => usePk(sale)) : []

        const adult_count = randomInt(1, 5)
        const child_count = randomBoolean() ? randomInt(0, adult_count) : 0
        const infant_count = randomBoolean() ? randomInt(0, adult_count - child_count) : 0

        const check_payment_ps = randomBoolean(0.5) ? 0 : 3.5

        return makeModelRecord(modelName, {
            lead_pk: randomElementPk(await findModelRecords('Lead')),
            created_by_pk: randomElementPk(await findModelRecords('Agent')), // TODO sales dep,
            created_at: dateToUnixTimestamp(created_at),
            itinerary_type: randomEnumValue(ItineraryType),
            has_fake_segments,
            need_verification: has_fake_segments ? randomBoolean() : false,
            adult_count,
            child_count,
            infant_count,
            is_hidden: randomBoolean(),
            active_till: dateToUnixTimestamp(faker.date.soon({ days: 3 })),
            is_sold_by_provider: randomBoolean(),

            client_remark: faker.string.sample(2),
            agent_remark: faker.string.sample(2),

            is_award: randomBoolean(0.1),
            is_sent,
            is_sent_at: is_sent ? dateToUnixTimestamp(faker.date.recent({ days: 1 })) : null,
            is_viewed,
            is_viewed_at: is_viewed ? dateToUnixTimestamp(faker.date.recent({ days: 1 })) : null,
            offer_pk: String(randomInt(1, 10)),

            sale_pks: sale_pks,
            check_payment_ps,
            is_editable: is_sent || sale_pks.length > 0,

            tour_fare_type: check_payment_ps ? randomBoolean() ? PqTourFareType.TourFareCk : null : randomBoolean() ? PqTourFareType.TourFare : null,

            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
