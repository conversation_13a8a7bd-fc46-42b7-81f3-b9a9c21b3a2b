import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'PriceQuoteAdditionalTrackingInfo'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class PriceQuoteAdditionalTrackingInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            is_tracked_by_pks: [],
            ...attributes,
        })
    }
}
