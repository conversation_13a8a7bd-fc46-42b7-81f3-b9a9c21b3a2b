import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

const modelName = 'PriceQuoteAdditionalSummary'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class PriceQuoteAdditionalSummaryFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    private calcSummaryItem(count = 0) {
        return {
            net_price: randomFloat(1000, 2000) * count,
            sell_price: randomFloat(1000, 2000) * count * randomFloat(0.95, 1.22),
            count: count,
        }
    }

    public async make(attributes: Attributes) {
        const adult_count = randomInt(1, 3)
        const child_count = randomInt(0, adult_count)
        const infant_count = randomInt(0, adult_count - child_count)

        const adult = this.calcSummaryItem(adult_count)
        const child = this.calcSummaryItem(child_count)
        const infant = this.calcSummaryItem(infant_count)
        const total = {
            net_price: adult.net_price + child.net_price + infant.net_price,
            sell_price: adult.sell_price + child.sell_price + infant.sell_price,
            count: adult.count + child.count + infant.count,
        }

        return makeModelRecord(modelName, {
            adult,
            child,
            infant,
            total,
            profit: total.sell_price - total.net_price,
            is_mark_up_alert: randomBoolean(),
            ...attributes,
        })
    }
}
