import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { ProductType } from '~/api/models/Product/Product'
import { randomElementPk, randomElements } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'

const modelName = 'Ticket'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class TicketFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        await findModelRecords('Agent')
        const ticketProducts = await findModelRecords('Product', { 'item_type': ProductType.Ticket })
        const milePricePrograms = await findModelRecords('MilePriceProgram')
        const airlines = await findModelRecords('Airline')
        const pnr = randomBoolean() ? faker.random.alphaNumeric(8) : null

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            product_pk: randomElementPk(ticketProducts),
            award_account_pk: null,
            sale_version_pk: String(randomInt(1, 10)),

            passenger_pk: randomElementPk(await findModelRecords('SaleVersionPassenger')),

            validating_carrier_pk: randomElementPk(await findModelRecords('Airline')),
            validating_carrier_pnr: pnr,

            mile_price_program_pk: randomElementPk(milePricePrograms),
            //
            airlines: pnr && randomBoolean() ? randomElements(airlines, randomInt(1, 3)).map((airline) => {
                return {
                    airline_pk: usePk(airline),
                    pnr: faker.random.alphaNumeric(8),
                }
            }) : null,
            ...attributes,
        })
    }
}
