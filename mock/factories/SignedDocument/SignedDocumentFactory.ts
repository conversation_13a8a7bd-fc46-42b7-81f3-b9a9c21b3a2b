import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { SignedDocumentCategory } from '~/api/models/SignedDocument/SignedDocument'

const modelName = 'SignedDocument'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SignedDocumentFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes = {},
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes = {},
    ) {
        return makeModelRecord(modelName, {
            //
            type: randomEnumValue(SignedDocumentCategory),
            sent_at: randomBoolean() ? Date.now() : null,
            viewed_at: randomBoolean() ? Date.now() : null,
            signed_at: randomBoolean() ? Date.now() : null,
            ...attributes,
        })
    }
}

type Options = {
    //
}
