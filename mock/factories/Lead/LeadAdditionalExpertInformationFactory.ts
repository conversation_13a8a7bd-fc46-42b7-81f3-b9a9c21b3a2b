import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp, randomBoolean, withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'

const modelName = 'LeadAdditionalExpertInformation'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class LeadAdditionalExpertInformationFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            external_price: withChance(randomInt(100, 5000)),
            requested_by_pk: randomElementPk(await findModelRecords('Agent')),
            expert_pk: randomElementPk(await findModelRecords('Agent')),
            expert_request_at: dateToUnixTimestamp(faker.date.past()),
            expert_request_is_done: randomBoolean(),
            expert_request_start_at: dateToUnixTimestamp(faker.date.past()),
            is_duplicate: randomBoolean(),
        })
    }
}
