import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { faker } from '@faker-js/faker'
import { withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'LeadAdditionalUTM'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class LeadAdditionalUTMFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            utm_ip_country: faker.location.countryCode().toLowerCase(),
            utm_ip: faker.internet.ipv4(),
            utm_ga: 'GA1.2.259890655.1635247759',
            utm_campaign: 'DSC_target-europe-mob',
            utm_medium: 'cpc',
            utm_source: withChance(faker.datatype.boolean()) ? 'Google' : 'Facebook',
            utm_term: '+business +class +flights',
            utm_content: 'SH-gen-kwd',
        })
    }
}
