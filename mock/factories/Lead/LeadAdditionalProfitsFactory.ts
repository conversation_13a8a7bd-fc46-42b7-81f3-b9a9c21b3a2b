import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, withChance } from '~mock/lib/Helper/SeedHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import type { ModelFields } from '~types/lib/Model'

const modelName = 'LeadAdditionalProfits'
type Attributes = Partial<ModelFields<typeof modelName>> & {
    id: number
}

export default class LeadAdditionalProfitsFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            tkt_profit: randomFloat(1000, 3000),
        })
    }
}
