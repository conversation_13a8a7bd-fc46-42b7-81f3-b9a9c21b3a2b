import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomFloat } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomBoolean } from '~mock/lib/Helper/SeedHelper'

const modelName = 'LeadExpertProfits'

type Attributes = Partial<ModelFields<typeof modelName>> & { lead_pk: string, agent_pk: string }

export default class LeadExpertProfitsFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            lead_pk: randomElementPk(await findModelRecords('Lead')),
            agent_pk: randomElementPk(await findModelRecords('Agent')),
            tkt_profit: randomFloat(10, 1000),
            lead_gp: randomFloat(10, 1000),
            pq_gp: randomFloat(10, 1000),
            is_manually_taken_from_queue: randomBoolean(),
        })
    }
}
