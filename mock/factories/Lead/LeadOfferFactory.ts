import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'

const modelName = 'LeadOffer'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class LeadOfferFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        const published = true // randomBoolean()
        const is_sent = published ? randomBoolean() : false
        const is_viewed = is_sent ? randomBoolean() : false
        const is_clicked = is_viewed ? randomBoolean() : false

        return makeModelRecord(modelName, {
            id: nextIncrementValue(modelName),
            lead_pk: randomElementPk(await findModelRecords('Lead')),
            created_at: dateToUnixTimestamp(faker.date.past()),
            is_to_client: randomBoolean(),
            is_published: published,

            is_mail_sent_at: is_sent ? dateToUnixTimestamp(faker.date.recent({ days: 2 })) : null,
            is_mail_viewed_at: is_viewed ? dateToUnixTimestamp(faker.date.recent({ days: 1 })) : null,
            is_mail_clicked_at: is_clicked ? dateToUnixTimestamp(faker.date.recent({ days: 1 })) : null,

            is_award_offer: randomBoolean(0.1),
            links: [faker.internet.url()],
            created_by_pk: randomElementPk(await findModelRecords('Agent')),
            //
            ...attributes,
        })
    }
}
