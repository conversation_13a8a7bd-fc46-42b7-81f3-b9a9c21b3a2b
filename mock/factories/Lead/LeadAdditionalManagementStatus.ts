import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { dateToUnixTimestamp, randomBoolean, withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelFields } from '~types/lib/Model'
import { faker } from '@faker-js/faker'

const modelName = 'LeadAdditionalManagementStatus'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class LeadAdditionalManagementStatusFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            start_working_date: withChance(dateToUnixTimestamp(faker.date.recent()), 0.2),
            session_start_date: dateToUnixTimestamp(faker.date.recent({ days: 8 })),
            is_in_queue: randomBoolean(0.2),
        })
    }
}
