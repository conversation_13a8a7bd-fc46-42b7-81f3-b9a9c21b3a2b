import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { faker } from '@faker-js/faker'
import { dateToUnixTimestamp, randomBoolean } from '~mock/lib/Helper/SeedHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import type { ModelFields } from '~types/lib/Model'
import { followUpMaxPoints } from '~/api/models/Lead/Lead'

const modelName = 'LeadAdditionalListInformation'
type Attributes = Partial<ModelFields<typeof modelName>> & {
    id: number
}

export default class LeadAdditionalListInformationFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            count: randomInt(0, 3),
            sent_count: randomInt(0, 3),
            price_quote_last_time: dateToUnixTimestamp(faker.date.past()),
            follow_up_completed_points: randomInt(0, followUpMaxPoints),
            is_mark_up_alert: randomBoolean(),
        })
    }
}
