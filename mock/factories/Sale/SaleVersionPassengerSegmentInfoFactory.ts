import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { PreTicketSellType, SegmentType } from '~/api/models/PriceQuote/PriceQuote'
import segmentsData from '~/mockData/segmentsData'

const modelName = 'SaleVersionPassengerSegmentInfo'

const lineRaws = segmentsData.map(item => item.raw_line)

type Attributes = Partial<ModelFields<typeof modelName>> & { sale_version_pk: PrimaryKey }

export default class SaleVersionPassengerSegmentInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const sale_version_pk = attributes.sale_version_pk
        const id = attributes.id ?? nextIncrementValue(modelName)

        const data = {
            id,
            seat_number: '19A',
            passenger_pk: randomElementPk(await findModelRecords('SaleVersionPassenger')),
            segment_pk: '1',
            segment_info: {
                id: randomInt(1, 10000),
                type: randomEnumValue(SegmentType),
                // flight_id: randomInt(1, 10000),
                is_return: randomBoolean(),
                group_index: randomInt(1, 4),
                is_private: randomBoolean(),
                pre_ticket_sell_type: randomEnumValue(PreTicketSellType),
                line_raw: randomElement(lineRaws),
                operated_by: randomBoolean() ? faker.string.sample(5) : null,
            },
            sale_version_pk,
        }

        return makeModelRecord(modelName, {
            ...data,
        })
    }
}

type Options = {
    //
}
