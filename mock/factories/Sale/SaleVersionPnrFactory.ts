import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { PnrFareType } from '~/api/models/Sale/SaleVersionPnr'
import { faker } from '@faker-js/faker'
import { tryRandomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'SaleVersionPnr'

type Attributes = Partial<ModelFields<typeof modelName>> & { sale_version_pk: PrimaryKey }

export default class SaleVersionPnrFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const sale_version_pk = attributes.sale_version_pk
        const id = attributes.id ?? nextIncrementValue(modelName)

        const data = {
            id,
            pnr: faker.string.alphanumeric(5),
            sale_version_pk,
            fare_type: randomEnumValue(PnrFareType),
            departure_date: dateToUnixTimestamp(faker.date.future()),
            last_ticket_pk: tryRandomElementPk(await findModelRecords('Ticket')),
            is_processed: randomBoolean(),
            in_queue: randomBoolean(),
            is_started_by_pk: tryRandomElementPk(await findModelRecords('Agent')),
            is_started_at: dateToUnixTimestamp(faker.date.past()),
        }

        return makeModelRecord(modelName, {
            ...data,
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
