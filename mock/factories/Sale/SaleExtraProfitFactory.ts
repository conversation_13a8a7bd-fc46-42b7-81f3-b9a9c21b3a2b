import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp } from '~mock/lib/Helper/SeedHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'

const modelName = 'SaleExtraProfit'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleExtraProfitFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            created_at: dateToUnixTimestamp(new Date()),
            project_pk: randomElementPk(await findModelRecords('Project')),
            ...attributes,
        })
    }
}
