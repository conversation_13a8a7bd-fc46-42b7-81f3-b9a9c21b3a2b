import type { ModelFields } from '~types/lib/Model'
import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomFloat, randomInt } from '~/lib/Helper/NumberHelper'
import { items } from '~mock/lib/Helper/SeedHelper'
import { faker } from '@faker-js/faker'
import { randomElement } from '~/lib/Helper/ArrayHelper'

const modelName = 'SaleAdditionalInfo'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class SaleAdditionalInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const airlines = await findModelRecords('Airline')

        return makeModelRecord(modelName, {
            passenger_name_record: items(randomInt(1, 3)).map(() => faker.string.alphanumeric(6).toUpperCase()),
            validation_carrier_code: attributes.validation_carrier_code ?? randomElement(airlines).code,
            tickets_count: randomInt(1, 10),
            net_price: randomFloat(200, 1000),
            sell_price: randomFloat(200, 1000),
            fee: randomFloat(200, 1000),
            profit: randomFloat(200, 1000),
            debt_amount: randomFloat(200, 1000),
            ...attributes,
        })
    }
}
