import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecord,
    findModelRecords,
    makeModelRecord,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelAttributes, ModelFields } from '~types/lib/Model'
import { randomElement } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'
import { SaleType } from '~/api/models/Sale/Sale'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { MockProjectID } from '~mock/seeds/models/Project/ProjectSeeder'
import { usePk } from '~/composables/usePk'

const modelName = 'SaleDraft'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleDraftFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const leads = await findModelRecords('Lead')

        const lead = randomElement(leads)
        const client = await findModelRecord('Client', lead.client_pk) as ModelAttributes<'Client'>

        return makeModelRecord(modelName, {
            client_pk: lead.client_pk,
            sale_at: dateToUnixTimestamp(faker.date.past({ years: 1 })),
            lead_pk: usePk(lead),
            project_pk: usePk('Project', MockProjectID.TBC),
            type: randomEnumValue(SaleType),

            client_first_name: client.first_name,
            client_last_name: client.last_name,
            from_iata_code: lead.from_iata_code,
            to_iata_code: lead.to_iata_code,
            is_test: randomBoolean(),
            //
            ...attributes,
        })
    }
}
