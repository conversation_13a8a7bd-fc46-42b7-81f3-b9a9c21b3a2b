import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import { PassengerAge } from '~/api/models/Sale/SaleVersionPassenger'
import { faker } from '@faker-js/faker'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { TicketPreferenceCategory } from '~/api/models/TicketExtraPreference/TicketExtraPreference'
import { Gender } from '~types/enums/Gender'

const modelName = 'SaleVersionPassenger'

type Attributes = Partial<ModelFields<typeof modelName>> & { sale_version_pk: PrimaryKey }

export default class SaleVersionPassengerFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const id = attributes.id ?? nextIncrementValue(modelName)
        const preferences = await findModelRecords('TicketExtraPreference')
        const airlines = (await findModelRecords('Airline')).slice(0, 20)

        const data = {
            passenger_type: randomEnumValue(PassengerAge),
            first_name: faker.person.firstName(),
            last_name: faker.person.lastName(),
            middle_name: faker.person.middleName(),
            sex: randomEnumValue(Gender),
            birthday_at: dateToUnixTimestamp(faker.date.birthdate()),
            global_entry_number: withChance(faker.string.alphanumeric(5), 0.5),
            known_traveler_number: withChance(faker.string.alphanumeric(5), 0.5),
            tsa_precheck_number: withChance(faker.string.alphanumeric(5), 0.5),
        }

        const frequent_flyers = []
        for (let i = 0; i < 2; i++) {
            frequent_flyers.push({
                airline_pk: randomElementPk(airlines),
                frequent_flyer_number: faker.string.numeric(6),
            })
        }

        return makeModelRecord(modelName, {
            id,
            ...data,
            old_data: {
                id: 1,
                ...data,
                extraSeatPreference: {
                    name: 'extraSeatPreference',
                },
                extraMealPreference: {
                    name: 'extraMealPreference',
                },
                extraSpecialPreference: {
                    name: 'extraSpecialPreference',
                },
                extra_seat_preference_id: 0,
                extra_meal_preference_id: 0,
                extra_special_preference_id: 0,
            },
            frequent_flyers,
            meal_preference_pk: !isTest
                ? randomElementPk(preferences.filter((item) => item.category === TicketPreferenceCategory.Meal))
                : null,
            special_preference_pk: !isTest
                ? randomElementPk(preferences.filter((item) => item.category === TicketPreferenceCategory.Special))
                : null,
            seat_preference_pk: !isTest
                ? randomElementPk(preferences.filter((item) => item.category === TicketPreferenceCategory.Seat))
                : null,
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
