import Factory from '~mock/factories/Factory'
import {
    createModelRecord,
    findModelRecords,
    makeModelRecord,
    nextIncrementValue,
} from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { dateToUnixTimestamp, randomBoolean, randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import { PnrInfoType } from '~/api/models/Sale/SaleVersionPnr'
import { randomInt } from '~/lib/Helper/NumberHelper'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { faker } from '@faker-js/faker'

const modelName = 'SaleVersionPnrInfo'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleVersionPnrInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
        options: Options = {},
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        const id = attributes.id ?? nextIncrementValue(modelName)

        const pnrRecords = await findModelRecords('SaleVersionPnr')

        const data = {
            id,
            pnr_pk: randomElementPk(pnrRecords),
            type: randomEnumValue(PnrInfoType),
            eligibility: randomBoolean(),
            penalty: randomInt(0, 10000),
            non_refundable_tax: randomBoolean(),
            validity: dateToUnixTimestamp(faker.date.future()),
            commission: randomInt(0, 100),
            refund_amount: randomInt(0, 1000),
            fop: 'fop-value',
            processed_by_pk: randomElementPk(await findModelRecords('Agent')),
        }

        return makeModelRecord(modelName, {
            ...data,
            //
            ...attributes,
        })
    }
}

type Options = {
    //
}
