import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomElementPk } from '~/lib/Helper/ArrayHelper'
import { randomFloat } from '~/lib/Helper/NumberHelper'
import { faker } from '@faker-js/faker'

const modelName = 'SaleVoucher'

type Attributes = Partial<ModelFields<typeof modelName>>

export default class SaleVoucherFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(
        attributes: Attributes,
    ) {
        return makeModelRecord(modelName, {
            voucher_id: faker.string.alpha(10),
            //
            sale_pk: randomElementPk(await findModelRecords('Sale')),
            product_pk: randomElementPk(await findModelRecords('Product')),
            airline_report_pk: randomElementPk(await findModelRecords('AirlineReport')),
            amount: randomFloat(30, 1000),
            remark: faker.word.words(4),
            project_pk: randomElementPk(await findModelRecords('Project')),
            //
            ...attributes,
        })
    }
}
