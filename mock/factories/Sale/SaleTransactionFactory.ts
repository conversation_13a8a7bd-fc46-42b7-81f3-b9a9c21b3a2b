import Factory from '~mock/factories/Factory'
import { createModelRecord, findModelRecords, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import type { ModelFields } from '~types/lib/Model'
import { randomEnumValue } from '~mock/lib/Helper/SeedHelper'
import {
    SaleTransactionActionType,
    SaleTransactionDirectionType,
    SaleTransactionStatus,
} from '~/api/models/Sale/SaleTransaction'
import { faker } from '@faker-js/faker'
import { randomElement, randomElementPk } from '~/lib/Helper/ArrayHelper'
import { CreditCardType } from '~/api/models/Sale/SaleVersionCard'

const modelName = 'SaleTransaction'

type Attributes = {sale_pk: PrimaryKey, client_card_pk: PrimaryKey, sale_version_card_pk: PrimaryKey } & Partial<ModelFields<typeof modelName>>

export default class SaleTransactionFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(
        attributes: Attributes,
    ) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        const possibleDescriptions = [
            'Flight Ticket To Dubai',
            'Ticket Protection - Flight Ticket',
            'Flight Ticket To Dubai',
            'Tips - Flight Ticket To Dubai',
            'Verify - Flight Ticket To Dubai',
        ]

        const status = randomEnumValue(SaleTransactionStatus)
        const settlement_amount = randomElement([faker.number.float({ min: 1, max: 300 })])
        const created_by_pk = randomElementPk(await findModelRecords('Agent'))

        return makeModelRecord(modelName, {
            pay_type: 'CC',
            action_type: randomEnumValue(SaleTransactionActionType),
            status: status,
            direction_type: randomEnumValue(SaleTransactionDirectionType),
            external_id: faker.word.noun(100),
            description: randomElement(possibleDescriptions),
            invoice: faker.word.words(),
            balance: faker.number.float(),
            settlement_amount: settlement_amount,
            settlement_at: settlement_amount ? Date.now() : null,
            canceled_at: status === SaleTransactionStatus.Canceled ? faker.date.anytime().getTime() : null,
            system_message: faker.lorem.lines(1),
            payment_gateway_pk: randomElementPk(await findModelRecords('PaymentGateway')),
            amount: faker.number.float(),
            created_at: Date.now(),
            external_data: {
                settlement: {
                    amount: faker.number.float(),
                    date: faker.date.anytime().getTime(),
                },
                authorize: {
                    amount: faker.number.float(),
                    date: faker.date.anytime().getTime(),
                    code: faker.location.countryCode(),
                    referenceTransactionId: faker.word.adjective(),
                    type: randomEnumValue(SaleTransactionActionType),
                    isRecurring: faker.datatype.boolean(),
                    isPartialCapture: faker.word.words(),
                    ip: faker.internet.ip(),
                },
                payment: {
                    cardType: randomEnumValue(CreditCardType),
                    cardNumber: faker.finance.creditCardNumber(),
                    cardExpiration: '11/25',
                    amount: faker.number.float(),
                },
                billing: {
                    firstName: faker.person.firstName(),
                    lastName: faker.person.lastName(),
                    address: faker.location.streetAddress(),
                    city: faker.location.city(),
                    state: faker.location.state(),
                    zip: faker.location.zipCode(),
                    country: faker.location.country(),
                    phone: faker.phone.number(),
                },
            },
            created_by_pk,
            ...attributes,
        })
    }
}
