import Factory from '~mock/factories/Factory'
import { createModelRecord, makeModelRecord } from '~mock/lib/Helper/ModelDatabaseHelper'
import { randomEnumValue, withChance } from '~mock/lib/Helper/SeedHelper'
import type { ModelFields } from '~types/lib/Model'
import { IssueStarColor } from '~/api/dictionaries/Static/Issue/IssueStarColorDictionary'

const modelName = 'IssueAdditionalAgentInfo'

type Attributes = Partial<ModelFields<typeof modelName>> & { id: number }

export default class IssueAdditionalAgentInfoFactory extends Factory<typeof modelName> {
    public readonly relatedModels: ModelName[] = [modelName]

    public async create(attributes: Attributes) {
        return createModelRecord(modelName, await this.make(attributes))
    }

    public async make(attributes: Attributes) {
        return makeModelRecord(modelName, {
            id: attributes.id,
            star_color: withChance(randomEnumValue(IssueStarColor), 0.5),
            //@ts-ignore
            auth_pk: '3',
        })
    }
}
